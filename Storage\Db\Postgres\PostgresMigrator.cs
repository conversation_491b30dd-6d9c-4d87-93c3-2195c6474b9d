using FluentMigrator;
using FluentMigrator.Builders.Alter.Table;
using FluentMigrator.Postgres;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace Levelbuild.Domain.Storage.Db.Postgres;

public class PostgresMigrator : SqlMigrator
{
	private static string AsFulltextSearchString(string language, IList<StorageFieldDefinitionOrm> fields) =>
		$"""
		 tsvector GENERATED ALWAYS AS (to_tsvector('{language}', 
		 	{string.Join(" || ' ' || ", GetFulltextsearchFields(fields)
							  .Select(field => field.Type is DataStoreFieldType.DateTime or DataStoreFieldType.Date
												   ? $"coalesce(immutable_date_to_string(\"{field.Name}\"), '')"
												   : $"coalesce(\"{field.Name}\"::text, '')"))})) STORED
		 """;
	
	public PostgresMigrator(Db db, ILogger logger) : base(db, logger)
	{
	}

	protected override void WithMultiValueField(Migration migration,
												IAlterTableAddColumnOrAlterColumnOrSchemaOrDescriptionSyntax alterTable,
												StorageIndexDefinition storageIndexDefinition, StorageFieldDefinitionOrm? storageFieldDefinitionOrm, IServiceScope? scope)
	{
		if (storageIndexDefinition.HasMultiValueFields())
		{
			if (storageFieldDefinitionOrm == null)
			{
				var multiValueFields = storageIndexDefinition.Fields.Where(it => it.MultiValue);
				foreach (var multiValueField in multiValueFields)
				{
					WithMultiValueField(migration, alterTable, storageIndexDefinition, multiValueField, scope);
				}
			}
			else
			{
				var col = alterTable
					.AddColumn(storageFieldDefinitionOrm.Name)
					.AsDbType(Db.DataHelper, storageFieldDefinitionOrm.Type, storageFieldDefinitionOrm.Length, true);
				SetColOptions(migration, storageIndexDefinition, storageFieldDefinitionOrm, col, true);
			}
		}
	}

	private protected override void AddDbFulltextSearch(string tableName, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		Migrate(it => { AddDbFulltextSearch(it, new PostgresLanguages()[Db.CustomerContext?.Language.Split("-")[0] ?? "en"], tableName, fields); }, scope);
	}

	internal static void AddDbFulltextSearch(Migration migration, string language, string tableName, IList<StorageFieldDefinitionOrm> fields)
	{
		migration.Alter.Table(tableName).AddColumn(StorageSystemField.SysFulltext.ToString())
			.AsCustom(AsFulltextSearchString(language, fields));

		migration.Create.Index($"{tableName}_fulltext_idx").OnTable(tableName)
			.OnColumn(StorageSystemField.SysFulltext.ToString())
			.Ascending().WithOptions()
			.UsingGin();
	}
	
	private protected override void RemoveDbFulltextSearch(string tableName, IServiceScope? scope)
	{
		Migrate(it =>
		{
			RemoveDbFulltextSearch(it, tableName);
		}, scope);
	}

	internal static void RemoveDbFulltextSearch(Migration migration, string tableName)
	{
		if (migration.Schema.Table(tableName).Index($"{tableName}_fulltext_idx").Exists())
		{
			migration.Delete.Index($"{tableName}_fulltext_idx").OnTable(tableName);
		}

		if (migration.Schema.Table(tableName).Column(StorageSystemField.SysFulltext.ToString()).Exists())
		{
			migration.Delete.Column(StorageSystemField.SysFulltext.ToString()).FromTable(tableName);
		}
	}

	private protected override void UpdateDbFulltextSearch(string tableName, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		// updates on generated columns are currently not supported in postgres.
		// the other way would be to fill the column through a trigger, as the trigger could be updated more easily
		RemoveDbFulltextSearch(tableName, scope);
		AddDbFulltextSearch(tableName, fields, scope);
	}
}