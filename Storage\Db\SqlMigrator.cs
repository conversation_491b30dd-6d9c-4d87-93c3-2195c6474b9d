using System.Data;
using System.Diagnostics.CodeAnalysis;
using Elastic.Clients.Elasticsearch.Analysis;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Elastic.Clients.Elasticsearch.Mapping;
using FluentMigrator;
using FluentMigrator.Builders;
using FluentMigrator.Builders.Alter.Table;
using FluentMigrator.Expressions;
using FluentMigrator.Infrastructure;
using FluentMigrator.Model;
using FluentMigrator.Postgres;
using FluentMigrator.Runner;
using FluentMigrator.Runner.Conventions;
using FluentMigrator.Runner.Initialization;
using FluentMigrator.Runner.Processors;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;
using Levelbuild.Domain.Storage.Db.Fluentmigrator;
using Levelbuild.Domain.Storage.Db.Fluentmigrator.ConventionSet;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog.Extensions.Logging;
using SharpCompress;
using SqlKata;
using SqlKata.Execution;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Domain.Storage.Db;

public abstract class SqlMigrator
{
	protected readonly ILogger Logger;

	protected Db Db;

	public SqlMigrator(Db db, ILogger logger)
	{
		Db = db;
		Logger = logger;
	}

	/// <summary>
	/// Executes the Migration either inside the provided transaction or inside a new one.
	/// </summary>
	/// <param name="t"></param>
	/// <param name="scope"></param>
	public void Migrate(Action<Migration> t, IServiceScope? scope)
	{
		var migration = new Mig(t);
		// Put the database update into a scope to ensure
		// that all resources will be disposed.
		if (scope != null)
		{
			UpdateDatabase(scope.ServiceProvider, migration);
			return;
		}

		using var serviceProvider = CreateMigrateServices(null, null);
		using var newScope = serviceProvider.CreateScope();
		UpdateDatabase(newScope.ServiceProvider, migration);
	}

	protected virtual void WithMultiValueField(Migration migration,
											   IAlterTableAddColumnOrAlterColumnOrSchemaOrDescriptionSyntax alterTable,
											   StorageIndexDefinition storageIndexDefinition, StorageFieldDefinitionOrm? storageFieldDefinitionOrm,
											   IServiceScope? scope)
	{
		// needs to be checked for existence in update functions
		if (storageIndexDefinition.HasMultiValueFields() && !migration.Schema
				.Table(storageIndexDefinition.GetMvfDefinition(true)!.Name).Exists())
			Create(storageIndexDefinition.GetMvfDefinition()!, scope);
		else
		{
			if (storageIndexDefinition.HasMultiValueFields())
				Create(storageIndexDefinition.GetMvfDefinition()!, new List<StorageFieldDefinitionOrm>() { storageFieldDefinitionOrm! }, scope);
		}
	}

	protected virtual void WithMultiValueLookup(Migration it, StorageIndexDefinition storageIndexDefinition, StorageFieldDefinitionOrm field,
												IServiceScope? scope)
	{
		var lookupSourceMappingTable = field.LookupSourceMappingTable ?? throw new DataStoreOperationException(
										   $"Field {field.Name} in DataSource {storageIndexDefinition.Name} is lookup and multivalue, but {nameof(field.LookupSourceMappingTable)} is null.");
		if (!it.Schema.Table(lookupSourceMappingTable).Exists())
			Create(storageIndexDefinition.GetMultivalueLookupDefinition(field.LookupSource!, lookupSourceMappingTable), scope, true);
	}

	private protected abstract void AddDbFulltextSearch(string tableName, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope);

	private protected abstract void RemoveDbFulltextSearch(string tableName, IServiceScope? scope);

	private protected abstract void UpdateDbFulltextSearch(string tableName, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope);

	protected internal void AddFulltextSearch(StorageIndexDefinition storageIndexDefinition, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		AddDbFulltextSearch(storageIndexDefinition.Name, fields, scope);
		AddElasticFulltextSearch(storageIndexDefinition, fields);
	}

	private void AddElasticFulltextSearch(StorageIndexDefinition storageIndexDefinition, IList<StorageFieldDefinitionOrm> fields)
	{
		if (Db.ElasticClient == null)
			return;

		RemoveElasticFulltextSearch(storageIndexDefinition);

		var elasticName = storageIndexDefinition.ElasticName(Db.CustomerContext!.ToDto());

		var summaryFields = fields.Where(it => it.FulltextIndexingType == FulltextIndexingType.Summary);

		var fieldProps = new Properties();
		var keywordProp = new Properties();
		keywordProp.Add("words", new TextProperty()
		{
			Fielddata = true,
			Analyzer = "keyword_analyzer"
		});
		summaryFields.ForEach(field => fieldProps.Add(field.Name, new TextProperty
		{
			Fields = keywordProp,
		}));
		Db.ElasticClient.Indices.Create(new CreateIndexRequest(elasticName)
		{
			Settings = new IndexSettings()
			{
				Index = new IndexSettings()
				{
					Analysis = new IndexSettingsAnalysis()
					{
						Analyzers = new Analyzers()
						{
							{
								"keyword_analyzer", new CustomAnalyzer
								{
									Tokenizer = "standard",
								}
							}
						}
					}
				}
			},
			Mappings = new TypeMapping
			{
				Properties = fieldProps
			}
		});
	}

	protected internal void RemoveFulltextSearch(StorageIndexDefinition storageIndexDefinition, IServiceScope? scope)
	{
		RemoveElasticFulltextSearch(storageIndexDefinition);
		RemoveDbFulltextSearch(storageIndexDefinition.Name, scope);
	}

	private void RemoveElasticFulltextSearch(StorageIndexDefinition storageIndexDefinition)
	{
		if (Db.ElasticClient == null)
			return;

		var elasticName = storageIndexDefinition.ElasticName(Db.CustomerContext!.ToDto());

		if (Db.ElasticClient.Indices.Exists(elasticName).Exists)
			Db.ElasticClient.Indices.Delete(elasticName);
	}

	protected static List<StorageFieldDefinitionOrm> GetFulltextsearchFields(IList<StorageFieldDefinitionOrm> fields)
	{
		return fields.Where(field => field.Type is DataStoreFieldType.Guid or DataStoreFieldType.String or DataStoreFieldType.Text or DataStoreFieldType.Date or DataStoreFieldType.DateTime &&
									 field is { MultiValue: false, Translatable: false, LookupSource: null })
			.ToHashSet().ToList();
	}

	public void Create(StorageIndexDefinition storageIndexDefinition, IServiceScope? scope, bool isMultiValueLookupMappingTable = false)
	{
		if (storageIndexDefinition.IsView)
		{
			CreateUpdateOrDeleteView(storageIndexDefinition, scope);
			return;
		}

		Migrate(it =>
		{
			var table = it.Create.Table(storageIndexDefinition.Name);
			foreach (var storageFieldDefinitionOrm in storageIndexDefinition.Fields.Where(field => !field.MultiValue))
			{
				if (storageFieldDefinitionOrm.Translatable)
					continue;

				if (storageFieldDefinitionOrm.LookupSource != null)
				{
					var col = table.WithColumn(storageFieldDefinitionOrm.Name)
						.AsDbType(Db.DataHelper, DataStoreFieldType.Guid);
					col.ForeignKey(storageFieldDefinitionOrm.LookupSource, StorageSystemField.Id.ToString())
						.OnDelete(isMultiValueLookupMappingTable ? Rule.Cascade : Rule.None);
					SetColOptions(it, storageIndexDefinition, storageFieldDefinitionOrm, col, true);
				}
				else
				{
					var col = table.WithColumn(storageFieldDefinitionOrm.Name)
						.AsDbType(Db.DataHelper, storageFieldDefinitionOrm.Type, storageFieldDefinitionOrm.Length);
					SetColOptions(it, storageIndexDefinition, storageFieldDefinitionOrm, col, true);
					if (storageFieldDefinitionOrm.PrimaryKey)
					{
						col.PrimaryKey();
						if (storageFieldDefinitionOrm.Type == DataStoreFieldType.Guid)
						{
							col.WithDefault(SystemMethods.NewGuid);
						}
						else
						{
							col.Identity();
						}
					}

					// special behavior for foreign key constraint
					if (storageFieldDefinitionOrm.ForeignKey.IsForeignKey)
					{
						col.ForeignKey(storageFieldDefinitionOrm.ForeignKey.PrimaryTableName,
									   storageFieldDefinitionOrm.ForeignKey.PrimaryTableColumnName)
							.OnDelete(storageFieldDefinitionOrm.ForeignKey.DeleteRule);
					}
				}
			}
		}, scope);

		Migrate(it => { WithMultiValueField(it, it.Alter.Table(storageIndexDefinition.Name), storageIndexDefinition, null, scope); }, scope);

		// needs to be checked for existence in update functions
		if (storageIndexDefinition.StoreRevisions)
		{
			var revDef = storageIndexDefinition.GetRevisionDefinition(true);
			Create(revDef!, scope);
		}

		if (storageIndexDefinition.FulltextSearch)
		{
			AddFulltextSearch(storageIndexDefinition, storageIndexDefinition.Fields, scope);
		}
	}

	private void CreateUpdateOrDeleteView(StorageIndexDefinition storageIndexDefinition, IServiceScope? scope, bool deleteView = false)
	{
		if (string.IsNullOrWhiteSpace(storageIndexDefinition.ViewDefinition))
			throw new DataStoreOperationException($"No view definition found for {storageIndexDefinition.Name}.");

		string sql = (!deleteView)
						 ? $"DROP VIEW IF EXISTS \"{storageIndexDefinition.Name}\" RESTRICT;CREATE OR REPLACE VIEW \"{storageIndexDefinition.Name}\" AS {storageIndexDefinition.ViewDefinition};"
						 : $"DROP VIEW IF EXISTS \"{storageIndexDefinition.Name}\"";
		if (scope != null)
		{
			var connection = scope.ServiceProvider.GetService<Func<IDbConnection>>() ??
							 throw new DataStoreOperationException($"Service Scope does not contain {nameof(Func<IDbConnection>)}.");
			Db.ConnectionHelper.WithQueryFactory(connection.Invoke(), factory => { factory.Statement(sql); });
		}
		else
		{
			Db.ConnectionHelper.WithTransaction(factory => { return factory.Statement(sql); });
		}
	}

	public void Create(StorageIndexDefinition storageIndexDefinition, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		Migrate(it =>
		{
			var table = it.Alter.Table(storageIndexDefinition.Name);
			foreach (var field in fields)
			{
				if (field.Translatable)
					continue;

				if (!field.MultiValue)
				{
					if (field.LookupSource != null)
					{
						var col = table.AddColumn(field.Name)
							.AsDbType(Db.DataHelper, DataStoreFieldType.Guid);
						col.ForeignKey(field.LookupSource, StorageSystemField.Id.ToString()).OnDelete(Rule.None);
						col.Indexed();
						SetColOptions(it, storageIndexDefinition, field, col, true);
					}
					else
					{
						var col = table.AddColumn(field.Name)
							.AsDbType(Db.DataHelper, field.Type, field.Length);
						SetColOptions(it, storageIndexDefinition, field, col, true);
						if (field.PrimaryKey)
							col.PrimaryKey().WithDefault(SystemMethods.NewGuid);

						if (field.ForeignKey.IsForeignKey)
						{
							col.ForeignKey(field.ForeignKey.PrimaryTableName, field.ForeignKey.PrimaryTableColumnName).OnDelete(field.ForeignKey.DeleteRule);
						}
					}
				}
				else
				{
					if (field.LookupSource == null)
						WithMultiValueField(it, table, storageIndexDefinition, field, scope);
					else
						WithMultiValueLookup(it, storageIndexDefinition, field, scope);
				}
			}
		}, scope);

		if (storageIndexDefinition.FulltextSearch && GetFulltextsearchFields(fields).Count > 0)
		{
			var fulltextFields = storageIndexDefinition.Fields.ToDictionary(it => it.Name);
			// potentially overwrite entries
			fields.ForEach(newField => fulltextFields[newField.Name] = newField);
			UpdateDbFulltextSearch(storageIndexDefinition.Name, fulltextFields.Values.ToList(), scope);
		}
	}

	public void Update(StorageIndexDefinition definition, IDictionary<string, StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		if (definition.IsView)
		{
			CreateUpdateOrDeleteView(definition, scope);
			return;
		}

		Rename(definition, fields.Where(it => it.Key != it.Value.Name && !it.Value.Translatable)
				   .ToDictionary(f => f.Key, f => f.Value.Name), scope);

		bool newFulltextField = false;
		bool recreateFulltextSearch = definition.FulltextSearch && GetFulltextsearchFields(fields.Values.ToList()).Count > 0;
		recreateFulltextSearch |=
			fields.Values.Any(field => field.MultiValue != definition.Fields.First(f => f.Name == field.Name).MultiValue);

		if (recreateFulltextSearch)
		{
			RemoveFulltextSearch(definition, scope);
		}

		foreach (var field in fields.Values)
		{
			if (field.Translatable)
				continue;

			var oldField = definition.Fields.First(f => f.Name == field.Name);
			
			if (field is { LookupSource: not null, MultiValue: true } && !oldField.MultiValue)
			{
				UpdateLookupFieldToMultivalueLookupField(definition, field, scope);
				continue;
			}

			Migrate(it =>
			{
				var uniqueIndexExisted = field.LookupSource == null &&
										 it.Schema.Table(definition.Name).Index(GetIndexName(definition.Name, field.Name)).Exists();
				bool uniqueChanged = uniqueIndexExisted != field.Unique;

				// if unique was deleted on field, then manually drop index on database field
				if (uniqueChanged && !field.Unique)
					it.Delete.Index().OnTable(definition.Name).OnColumn(field.Name);
				
				if (field.LookupSource == null && field.MultiValue)
					it.Delete.DefaultConstraint().OnTable(definition.Name).OnColumn(field.Name);

				IAlterTableColumnAsTypeSyntax columnAsTypeSyntax = it.Alter.Table(definition.Name).AlterColumn(field.Name);
				IAlterTableColumnOptionOrAddColumnOrAlterColumnSyntax col;
				if (field.LookupSource != null)
				{
					col = columnAsTypeSyntax.AsDbType(Db.DataHelper, DataStoreFieldType.Guid);
				}
				else
				{
					var oldType = definition.Fields.First(f => f.Name == field.Name).Type;
					if (oldType != field.Type
						&& oldType != DataStoreFieldType.String && oldType != DataStoreFieldType.Text
						&& field.Type == DataStoreFieldType.String && field.Type == DataStoreFieldType.Text)
						newFulltextField = true;

					var extraUsing = $"""
									  (
									    CASE
									      WHEN "{field.Name}" IS NULL {(field.Type is DataStoreFieldType.String or DataStoreFieldType.Text ? $"OR \"{field.Name}\" = '' " : "")}THEN NULL
									      ELSE ARRAY["{field.Name}"]
									    END
									  )
									  """;
					col = columnAsTypeSyntax.AsDbType(Db.DataHelper, field.Type, field.Length, field.MultiValue, !oldField.MultiValue && field.MultiValue ? extraUsing : null);
					if (field.ForeignKey.IsForeignKey)
					{
						col.ForeignKey(field.ForeignKey.PrimaryTableName, field.ForeignKey.PrimaryTableColumnName).OnDelete(field.ForeignKey.DeleteRule);
					}
				}

				SetColOptions(it, definition, field, col, uniqueChanged);

				// we need to do this in an extra step, otherwise conversions from date {Nullable = true} to string {Nullable = false} will fail,
				// because string default value will be inserted into date column
				if (field is { Nullable: false, DefaultValue: not null })
					col.Nullable();
			}, scope);
		}

		// fix nullable field types AFTER data type conversions took place
		Migrate(it =>
		{
			foreach (var field in fields.Values.Where(field => field is { Nullable: false, DefaultValue: not null }))
			{
				Action<QueryFactory> updateEntries = qf =>
				{
					qf.Query(definition.Name).WhereNull(field.Name).Update(new Dictionary<string, object?>()
					{
						{ field.Name, field.DefaultValue }
					});
				};
				if (scope != null)
				{
					var connection = scope.ServiceProvider.GetService<Func<IDbConnection>>() ??
									 throw new DataStoreOperationException($"Service Scope does not contain {nameof(Func<IDbConnection>)}.");
					Db.ConnectionHelper.WithQueryFactory(connection.Invoke(), updateEntries);
				}
				else
				{
					Db.ConnectionHelper.WithQueryFactory(updateEntries);
				}

				var columnAsTypeSyntax = it.Alter.Table(definition.Name).AlterColumn(field.Name);
				var col = field.LookupSource != null
							  ? columnAsTypeSyntax.AsDbType(Db.DataHelper, DataStoreFieldType.Guid)
							  : columnAsTypeSyntax.AsDbType(Db.DataHelper, field.Type, field.Length);
				col.NotNullable();
				SetDefaultValue(field, col);
			}
		}, scope);

		if (recreateFulltextSearch)
		{
			var fulltextFields = definition.Fields.ToDictionary(it => it.Name);
			// potentially overwrite entries
			fields.Values.ForEach(newField => fulltextFields[newField.Name] = newField);
			AddFulltextSearch(definition, fulltextFields.Values.ToList(), scope);
		}
		else if (definition.FulltextSearch && newFulltextField)
		{
			var fulltextFields = definition.Fields.ToDictionary(it => it.Name);
			// potentially overwrite entries
			fields.Values.ForEach(newField => fulltextFields[newField.Name] = newField);
			UpdateDbFulltextSearch(definition.Name, fulltextFields.Values.ToList(), scope);
		}
	}

	private static void SetDefaultValue<TNext, TNextFk>(StorageFieldDefinitionOrm field,
														IColumnOptionSyntax<TNext, TNextFk> col)
		where TNext : IFluentSyntax
		where TNextFk : IFluentSyntax
	{
		if (field.DefaultValue!.ToString() != null
			&& Enum.IsDefined(typeof(SystemMethods), field.DefaultValue.ToString()!)
			&& Enum.TryParse(field.DefaultValue.ToString(), out SystemMethods method))
			col.WithDefault(method);
		else
		{
			var defaultValue = SqlDataHelper.GetDefaultValue(field);
			if (defaultValue != null)
				col.WithDefaultValue(defaultValue);
		}
	}

	protected internal static string GetIndexName(string dataSource, string column)
	{
		var cry = new CreateIndexExpression();
		cry.Index.TableName = dataSource;
		cry.Index.Columns.Add(new IndexColumnDefinition()
		{
			Name = column
		});
		var conventions = new LimitCharactersConventionSet(new DefaultConventionSet());
		conventions.IndexConventions.ForEach(convention => convention.Apply(cry));
		return cry.Index.Name;
	}

	protected internal static string GetConstraintName(bool isPrimaryKey, string dataSource, string column)
	{
		var cry = new CreateConstraintExpression(isPrimaryKey ? ConstraintType.PrimaryKey : ConstraintType.Unique);
		cry.Constraint.TableName = dataSource;
		if (!isPrimaryKey)
			cry.Constraint.Columns.Add(column);
		var conventions = new LimitCharactersConventionSet(new DefaultConventionSet());
		conventions.ConstraintConventions.ForEach(convention => convention.Apply(cry));
		return cry.Constraint.ConstraintName;
	}

	protected internal static string GetForeignKeyConstraintName(string dataSource, string column, string referencedDataSource, string? referencedColumn = null)
	{
		if (referencedColumn == null)
			referencedColumn = StorageSystemField.Id.ToString();

		var cry = new CreateForeignKeyExpression();
		cry.ForeignKey.PrimaryTable = referencedDataSource;
		cry.ForeignKey.PrimaryColumns.Add(referencedColumn);
		cry.ForeignKey.ForeignTable = dataSource;
		cry.ForeignKey.ForeignColumns.Add(column);
		var conventions = new LimitCharactersConventionSet(new DefaultConventionSet());
		conventions.ForeignKeyConventions.ForEach(convention => convention.Apply(cry));
		return cry.ForeignKey.Name;
	}

	public void Rename(StorageIndexDefinition definition, IDictionary<string, string> oldNewColumnNames, IServiceScope? scope)
	{
		if (oldNewColumnNames.Count == 0)
			return;

		Migrate(it =>
		{
			foreach (var namePair in oldNewColumnNames)
			{
				var field = definition.Fields.Where(it => (it.Name == namePair.Key)).FirstOrDefault();
				if ((field != null) && (field.LookupSource != null))
				{
					// artificial field; does not exist
					if (field.MultiValue)
						continue;

					it.Delete.Index().OnTable(definition.Name).OnColumn(field.Name);
					it.Delete.ForeignKey().FromTable(definition.Name).ForeignColumn(field.Name)
						.ToTable(field.LookupSource).PrimaryColumn(StorageSystemField.Id.ToString());
				}

				it.Rename.Column(namePair.Key).OnTable(definition.Name).To(namePair.Value);

				if ((field != null) && (field.LookupSource != null))
				{
					field.Name = namePair.Value;
					it.Create.ForeignKey().FromTable(definition.Name).ForeignColumn(field.Name)
						.ToTable(field.LookupSource).PrimaryColumn(StorageSystemField.Id.ToString());
					it.Create.Index().OnTable(definition.Name).OnColumn(field.Name);
				}
			}
		}, scope);
	}

	public void Delete(StorageIndexDefinition definition, IList<StorageFieldDefinitionOrm> fields, IServiceScope? scope)
	{
		if (definition.FulltextSearch)
		{
			if (GetFulltextsearchFields(fields).Count > 0)
			{
				var fieldNames = fields.Select(it => it.Name).ToList();
				UpdateDbFulltextSearch(definition.Name, definition.Fields.Where(field => !fieldNames.Contains(field.Name)).ToList(), scope);
			}
		}

		Migrate(it =>
		{
			var deletedMappingTables = new HashSet<string>();
			foreach (var field in fields)
			{
				if (field.LookupSource != null && field.MultiValue)
				{
					void RemoveMappingTable(StorageDatabaseContext connection)
					{
						if (!deletedMappingTables.Add(field.LookupSourceMappingTable!))
							return;

						var otherFieldIds = fields.Where(f => f.LookupSourceMappingTable == field.LookupSourceMappingTable).Select(f => f.Id).ToList();
						// as this connection might also be used by other tables, we need to make sure this is not the case before deleting it.
						var otherFields = connection.StorageFieldDefinition
							.Any(fieldDefinition =>
									 fieldDefinition.LookupSourceMappingTable == field.LookupSourceMappingTable &&
									 !otherFieldIds.Contains(fieldDefinition.Id));

						if (!otherFields)
						{
							it.Delete.Table(field.LookupSourceMappingTable);
						}
					}

					if (scope != null)
					{
						var connection = scope.ServiceProvider.GetService<Func<StorageDatabaseContext>>()?.Invoke() ??
										 throw new DataStoreOperationException($"Service Scope does not contain {nameof(IDbConnection)}.");
						RemoveMappingTable(connection);
					}
					else
					{
						using var connection = Db.GetEfCoreDbContext();
						RemoveMappingTable(connection);
					}
				}
				else if (field.LookupSource != null)
				{
					it.Delete.Index().OnTable(definition.Name).OnColumn(field.Name);
					it.Delete.ForeignKey().FromTable(definition.Name).ForeignColumn(field.Name)
						.ToTable(field.LookupSource).PrimaryColumn(StorageSystemField.Id.ToString());
				}

				if (field.LookupSource == null || !field.MultiValue)
					it.Delete.Column(field.Name).FromTable(definition.Name);
			}
		}, scope);
	}

	public void Delete(StorageIndexDefinition storageIndexDefinition, IServiceScope? scope)
	{
		if (storageIndexDefinition.IsView)
		{
			CreateUpdateOrDeleteView(storageIndexDefinition, scope, true);
			return;
		}

		Migrate(it =>
		{
			if (it.Schema.Table(storageIndexDefinition.MvfTable).Exists())
				it.Delete.Table(storageIndexDefinition.MvfTable);
			if (it.Schema.Table(storageIndexDefinition.Name).Exists())
				it.Delete.Table(storageIndexDefinition.Name);
		}, scope);

		if (Db.ElasticClient != null && Db.ElasticClient.Indices.Exists(storageIndexDefinition.ElasticName(Db.CustomerContext!.ToDto())).Exists)
			Db.ElasticClient.Indices.Delete(storageIndexDefinition.ElasticName(Db.CustomerContext!.ToDto()));
	}

	private void UpdateLookupFieldToMultivalueLookupField(StorageIndexDefinition definition, StorageFieldDefinitionOrm field, IServiceScope? scope)
	{
		Create(definition, new List<StorageFieldDefinitionOrm>() { field }, scope);
		Action<QueryFactory> updateEntries = qf =>
		{
			qf.Query(field.LookupSourceMappingTable)
				.Insert([StorageSystemField.StorageFieldDefinitionId.ToString(), StorageSystemField.SysOrderBy.ToString(), definition.Name, field.LookupSource],
						  new Query(definition.Name)
							  .SelectRaw("?", definition.Fields.First(f => f.Name == field.Name).Id)
							  .SelectRaw("?", 0)
							  .Select(StorageSystemField.Id.ToString(), field.Name)
							  .WhereNotNull(field.Name));
		};
		if (scope != null)
		{
			var connection = scope.ServiceProvider.GetService<Func<IDbConnection>>() ??
							 throw new DataStoreOperationException($"Service Scope does not contain {nameof(Func<IDbConnection>)}.");
			Db.ConnectionHelper.WithQueryFactory(connection.Invoke(), updateEntries);
		}
		else
		{
			Db.ConnectionHelper.WithQueryFactory(updateEntries);
		}
		
		Migrate(it =>
		{
			it.Delete.Column(field.Name).FromTable(definition.Name);
		}, scope);
	}

	protected void SetColOptions<TNext, TNextFk>(Migration migration, StorageIndexDefinition sid, StorageFieldDefinitionOrm field,
												 IColumnOptionSyntax<TNext, TNextFk> col, bool uniqueChanged)
		where TNext : IFluentSyntax
		where TNextFk : IFluentSyntax
	{
		if (field.Nullable)
			col.Nullable();
		else
		{
			col.NotNullable();
		}

		if (field is { PrimaryKey: false, Unique: true } && uniqueChanged)
		{
			migration.Create.Index()
				.OnTable(sid.Name)
				.OnColumn(field.Name)
				.Unique()
				.WithOptions()
				.Filter($"\"{StorageSystemField.SysIsDeleted}\" is false");
		}

		if (field.DefaultValue != null)
		{
			SetDefaultValue(field, col);
		}
	}

	/// <summary>
	///     Configure the dependency injection services
	/// </summary>
	public ServiceProvider CreateMigrateServices(StorageDatabaseContext connection, IDbContextTransaction transaction)
	{
		if (transaction == null && connection != null || transaction != null && connection == null)
			throw new DataStoreOperationException($"Connection '{connection}' and Transaction '{transaction}' can only both be null or both be unset.");

		var serilogLoggerProvider = new SerilogLoggerProvider(Logger);
		var services = new ServiceCollection();

		if (transaction != null)
		{
			services.AddScoped<TransactionalPostgresProcessor>()
				.AddScoped<IMigrationProcessor>(sp => sp.GetRequiredService<TransactionalPostgresProcessor>())
				// we need to use Func here, because all used IDisposables will be disposed together with the ServiceProvider Scope
				.AddScoped<Func<IDbConnection>>(_ => connection!.Database.GetDbConnection)
				.AddScoped<Func<IDbTransaction>>(_ => transaction.GetDbTransaction)
				.AddScoped<Func<StorageDatabaseContext>>(_ => () => connection!)
				.AddScoped<Func<IDbContextTransaction>>(_ => () => transaction);
			services.Configure<RunnerOptions>(opts => opts.TransactionPerSession = true);
			services.AddOptions<SelectingProcessorAccessorOptions>()
				.Configure(it => it.ProcessorId = ProcessorId.Postgres);
		}

		services
			.AddFluentMigratorCore()
			.AddSingleton<IConventionSet>(new LimitCharactersConventionSet())
			.ConfigureRunner(rb =>
			{
				rb
					.WithGlobalConnectionString(Db.ConnectionString)
					.WithGlobalCommandTimeout(TimeSpan.FromHours(1))
					.AddPostgres();
			})
			.AddLogging(lb => lb.AddProvider(serilogLoggerProvider));

		return services.BuildServiceProvider(false);
	}

	/// <summary>
	///     Update the database
	/// </summary>
	private void UpdateDatabase(IServiceProvider serviceProvider, IMigration migration)
	{
		// Instantiate the runner
		var runner = serviceProvider.GetRequiredService<IMigrationRunner>();
		runner.Up(migration);
	}

	private class Mig : Migration
	{
		private readonly Action<Migration> _t;

		public Mig(Action<Migration> t)
		{
			_t = t;
		}

		public override void Up()
		{
			_t(this);
		}

		[ExcludeFromCodeCoverage]
		public override void Down()
		{
		}
	}

	/// <summary>
	/// Delete all constraints for the given table
	/// </summary>
	/// <param name="def"></param>
	/// <param name="scope"></param>
	public void DeleteConstraints(StorageIndexDefinition def, IServiceScope? scope)
	{
		Migrate(it =>
		{
			var fields = def.Fields.Where(it => (it.LookupSource != null)).ToList();
			var tables = new HashSet<string>();
			foreach (var field in fields)
			{
				if (!field.MultiValue)
				{
					// delete FK
					it.Delete.ForeignKey().FromTable(def.Name).ForeignColumn(field.Name)
						.ToTable(field.LookupSource).PrimaryColumn(StorageSystemField.Id.ToString());
				}
				else
				{
					if (it.Schema.Table(field.LookupSourceMappingTable).Exists()
						&& tables.Add(field.LookupSourceMappingTable!))
						it.Delete.Table(field.LookupSourceMappingTable);
				}
			}
		}, scope);
	}
}