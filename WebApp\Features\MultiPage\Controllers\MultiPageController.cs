﻿using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.FileUpload;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.FileUpload;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.WebSockets;
using Levelbuild.Frontend.WebApp.Features.WebSockets.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Extensions;
using SharpCompress;
using ClosedXML.Excel;
using Newtonsoft.Json;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using DataType = Levelbuild.Core.SharedDtos.Enums.DataType;
using Levelbuild.Frontend.WebApp.Features.SinglePage.Controllers;
using Levelbuild.Core.FrontendDtos.ExcelPresets;

namespace Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;

/// <summary>
/// Controller for multi data pages in the user section
/// </summary>
public partial class MultiPageController : FrontendController
{
	private readonly CoreDatabaseContext _databaseContext;
	private readonly UserManager _userManager;
	private IExtendedStringLocalizerFactory StringLocalizerFactory;

	/// <summary>
	/// default constructor
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="contextFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	public MultiPageController(ILogManager logManager, UserManager userManager, IDbContextFactory<CoreDatabaseContext> contextFactory,
							   IVersionReader versionReader, IExtendedStringLocalizerFactory stringLocalizerFactory) : base(
		logManager, logManager.GetLoggerForClass<MultiPageController>(), versionReader)
	{
		_databaseContext = contextFactory.CreateDbContext();
		_userManager = userManager;
		StringLocalizerFactory = stringLocalizerFactory;
	}

	/// <summary>
	/// Returns a mutated list of data store elements as JSON.
	/// </summary>
	/// <param name="sourceGuid">ID of the data source configuration</param>
	/// <param name="parameters">an object to query a Subset of cultures</param>
	[HttpGet("/Api/DataSources/{sourceGuid:guid}/Elements")]
	public ActionResult<FrontendResponse> Query(Guid sourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.Include(dataSource => dataSource.Workflows).ThenInclude(workflow => workflow.StatusField)
			.Include(dataSource => dataSource.Workflows).ThenInclude(workflow => workflow.Nodes)
			.AsSplitQuery()
			.FirstOrDefault(source => source.Id == sourceGuid);

		if (dataSourceEntity == null)
			throw new ElementNotFoundException($"DataSource with id: {sourceGuid} could not be found");

		var parameterFields = parameters.Fields?.Select(field => field.ToLower()).ToList();
		var dataFields = dataSourceEntity.Fields.Where(field => parameterFields == null || parameterFields.Count == 0 || parameterFields.Contains(field.Name.ToLower())).ToList();
		var queryFields = dataFields
			.Where(field => field.FieldType == DataFieldType.DataField)
			.Select(field => new DataStoreQueryField(field.Name)).ToList();

		var lookupFields = dataFields.Where(field => field is { LookupSourceId: not null })
			.Select(field => new DataStoreQueryField(field.LookupDisplayFieldId != null ? field.Name + "." + field.LookupDisplayField!.Name : field.Name,
													 field.Name)).ToList();

		if (lookupFields.Count > 0)
			queryFields.AddRange(lookupFields);

		var virtualFields = dataFields.Where(field => field.HasVirtualData)
			.Select(field => new DataStoreQueryField(field.VirtualDataStoreQueryName!, field.Name)).ToList();
		if (virtualFields.Count > 0)
			queryFields.AddRange(virtualFields);

		// add file fields if needed
		if (parameters.IncludeFileInfo)
		{
			var tempFieldNames = new List<string> { "SysFileSize", "SysFileDate", "SysFileType", "SysModifyUser", "SysCreateUser" };
			var tempQueryFields = tempFieldNames.Where(tempQueryField => queryFields.All(queryField => queryField.Name != tempQueryField))
				.Select(name => new DataStoreQueryField(name)).ToList();
			queryFields.AddRange(tempQueryFields);
		}

		// WF fields
		dataSourceEntity.Workflows.Where(workflow => workflow.Enabled).ForEach(workflow =>
		{
			if (workflow.StatusField != null && queryFields.All(field => field.Name != workflow.StatusField.Name))
				queryFields.Add(new DataStoreQueryField(workflow.StatusField.Name));
		});

		// we always need to load the pk field
		// TODO: currently this field is always called "id" and we can't select which field of the definition is the primary key -> this needs to change somewhere in the future ;)
		if (queryFields.Find(field => field.Name == "Id") == null)
			queryFields.Add(new DataStoreQueryField("Id"));

		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters != null)
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
			{
				//@TODO Workaround - @Eric - can be removed if prepareForDataStoreQuery is working for this endpoint
				PrepareFrontendFilters(dataSourceEntity, frontendFilters);
				query.WithFilter(frontendFilters);
			}
		}

		// add sorting
		if (parameters.Sortings is { Count: > 0 })
		{
			query.WithOrderBy(parameters.Sortings
								  .Select(sorting => new DataStoreElementSort(sorting.OrderColumn,
																			  DataStoreElementSortDirectionExtensions.Parse(
																				  sorting.Direction.GetDisplayName())))
								  .ToList());
		}

		// add pagination
		if (parameters.Limit > 0)
		{
			query.WithPaging(parameters.Limit, parameters.Offset);
			query.WithCountAll();
		}

		query.PrepareSortingsForDataStore(dataSourceEntity.Fields);

		// send request to the data store and query the data
		QueryResultDto<DataElementDto> queryResultDto;
		IList<DataElementDto> elements = [];
		try
		{
			var result = dataSourceEntity.GetElements(query);

			foreach (var element in result)
			{
				element.PrepareValuesForResponse(dataSourceEntity.Fields, queryFields);
				var dataElement = new DataElementDto(element.Id, element.Values)
				{
					Favorite = element.IsFavourite,
					Inactive = element.IsInactive,
					WorkflowInfos = GetWorkflowInfos(dataSourceEntity, element)
				};
				elements.Add(dataElement);

				if (element.FileInfo == null)
					continue;

				var fileInfoDto = new FileInfoDto
				{
					FileId = element.FileInfo.Id,
					DataSourceId = dataSourceEntity.Id,
					ElementId = element.Id
				};

				if (element.Values.TryGetValue("SysFileSize", out var fileSize))
					fileInfoDto.FileSize = (long)fileSize!;
				if (element.Values.TryGetValue("SysFileType", out var fileType))
					fileInfoDto.FileType = (string)fileType!;
				if (element.Values.TryGetValue("SysFileDate", out var fileDate))
					fileInfoDto.FileDate = ((DateTime)fileDate!).ToUniversalTime();
				if (element.Values.TryGetValue("SysModifyUser", out var modifyUser))
					fileInfoDto.FileChangedUser = (string)modifyUser!;
				if (element.Values.TryGetValue("SysCreateUser", out var createUser))
					fileInfoDto.FileCreateUser = (string)createUser!;

				dataElement.FileInfo = fileInfoDto;
			}

			queryResultDto = new QueryResultDto<DataElementDto>()
			{
				Rows = elements,
				CountTotal = result.CountTotal
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			queryResultDto = new QueryResultDto<DataElementDto>()
			{
				Rows = [],
				CountTotal = 0
			};
		}

		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// load Annotations for datasource
	/// </summary>
	/// <param name="sourceGuid"></param>
	/// <param name="parameters"></param>
	/// <returns></returns>
	/// TODO: make endpoint cancelable (request may get aborted if user leaves dataset before request is finished)
	[HttpGet("/Api/DataSources/{sourceGuid:guid}/Annotations")]
	public ActionResult<FrontendResponse> QueryAnnotations(Guid sourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields)
			.Include(dataSource => dataSource.AnnotationGroupByField)
			.Include(dataSource => dataSource.Workflows)
				.ThenInclude(workflow => workflow.StatusField)
			.Include(dataSource => dataSource.Workflows)
				.ThenInclude(workflow => workflow.Nodes)
			.FirstOrDefault(dataSource => dataSource.Id == sourceGuid);

		if (dataSourceEntity == null)
			throw new ElementNotFoundException($"DataSource with id: {sourceGuid} could not be found");

		var queryFields = new List<DataStoreQueryField>
		{
			new("Id"),
			new("AnnotationX"),
			new("AnnotationY"),
			new("SysCreateDate", "AnnotationCreateDate"),
			new("SysCreateUser", "AnnotationCreateUser")
		};

		// Description field(s)
		if (dataSourceEntity.AnnotationLabel?.Contains("##") == true)
		{
			PlaceholderRegex().Matches(dataSourceEntity.AnnotationLabel).ForEach(match =>
			{
				var fieldName = match.Groups[1].Value;
				if (queryFields.Find(queryField => queryField.Name == fieldName) == null &&
					dataSourceEntity.Fields.FirstOrDefault(field => field.Name == fieldName) != null)
					queryFields.Add(new DataStoreQueryField("Description"));
			});
		}

		// file info fields
		var tempFieldNames = new List<string> { "SysFileSize", "SysFileDate", "SysFileType", "SysModifyUser", "SysCreateUser" };
		var tempQueryFields = tempFieldNames.Where(tempQueryField => queryFields.All(queryField => queryField.Name != tempQueryField))
			.Select(name => new DataStoreQueryField(name)).ToList();
		queryFields.AddRange(tempQueryFields);

		// WF fields
		dataSourceEntity.Workflows.Where(workflow => workflow.Enabled).ForEach(workflow =>
		{
			if (workflow.StatusField != null)
				queryFields.Add(new DataStoreQueryField(workflow.StatusField.Name));
		});

		if (dataSourceEntity.AnnotationGroupByField != null)
			queryFields.Add(new DataStoreQueryField(dataSourceEntity.AnnotationGroupByField.Name, "AnnotationGroupById"));

		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters != null)
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
				query.WithFilter(frontendFilters);
		}

		// query elements
		var elements = dataSourceEntity.GetElements(query);
		var queryResultDto = new QueryResultDto<DataElementDto>
		{
			Rows = elements.Select(element =>
			{
				// prepare Label
				element.Values.Add("AnnotationDescription", PageUtils.ReplacePlaceholders(dataSourceEntity.AnnotationLabel ?? "", element.Values));

				var dataElement = new DataElementDto(element.Id, element.Values);

				if (element.FileInfo != null)
				{
					var fileInfoDto = new FileInfoDto
					{
						FileId = element.FileInfo.Id,
						DataSourceId = dataSourceEntity.Id,
						ElementId = element.Id
					};

					if (element.Values.TryGetValue("SysFileSize", out var fileSize))
						fileInfoDto.FileSize = (long)fileSize!;
					if (element.Values.TryGetValue("SysFileType", out var fileType))
						fileInfoDto.FileType = (string)fileType!;
					if (element.Values.TryGetValue("SysFileDate", out var fileDate))
						fileInfoDto.FileDate = ((DateTime)fileDate!).ToUniversalTime();
					if (element.Values.TryGetValue("SysModifyUser", out var modifyUser))
						fileInfoDto.FileChangedUser = (string)modifyUser!;
					if (element.Values.TryGetValue("SysCreateUser", out var createUser))
						fileInfoDto.FileCreateUser = (string)createUser!;

					dataElement.FileInfo = fileInfoDto;
				}

				dataElement.WorkflowInfos = GetWorkflowInfos(dataSourceEntity, element, true);
				return dataElement;
			}).ToList(),
			CountTotal = elements.CountTotal
		};
		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Returns a mutated list of data store elements grouped by the value including counts.
	/// </summary>
	/// <param name="dataFieldGuid">ID of the used data field configuration</param>
	/// <param name="parameters">an object to query a Subset of cultures</param>
	[HttpGet("/Api/DataFields/{dataFieldGuid:guid}/Elements/")]
	public ActionResult<FrontendResponse> GroupFilterFieldValues(Guid dataFieldGuid, QueryParamsDto parameters)
	{
		var dataFieldEntity = _databaseContext.DataFields
			.Include(dataField => dataField.LookupDisplayField)
			.Include(dataField => dataField.VirtualDataField)
			.Include(dataField => dataField.VirtualLookupField)
			.FirstOrDefault(dataField => dataField.Id == dataFieldGuid);

		if (dataFieldEntity == null)
		{
			throw new ElementNotFoundException($"Field with id: {dataFieldGuid} could not be found");
		}

		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.AsSplitQuery()
			.First(dataSource => dataSource.Id == dataFieldEntity.DataSourceId);

		const string countAlias = "Count";
		const string countName = "Count(*)";
		var filterFieldLabel = dataFieldEntity.FieldType switch
		{
			DataFieldType.LookupField => $"{dataFieldEntity.Name}.{dataFieldEntity.LookupDisplayField?.Name}",
			DataFieldType.VirtualField => dataFieldEntity.VirtualDataStoreQueryName ?? dataFieldEntity.Name,
			_ => dataFieldEntity.Name
		};
		var queryFields = new List<DataStoreQueryField> { new(filterFieldLabel), new(countName, countAlias) };
		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters is { Count: > 0 })
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
			{
				//@TODO Workaround - @Eric - can be removed if prepareForDataStoreQuery is working for this endpoint
				PrepareFrontendFilters(dataSourceEntity, frontendFilters);
				query.WithFilter(frontendFilters);
			}
		}

		query.WithOrderBy([new DataStoreElementSort(countName, DataStoreElementSortDirection.Desc), new DataStoreElementSort(filterFieldLabel)]);
		query.WithGroupBy([filterFieldLabel]);
		query.WithPaging(51);

		// send request to the data store and query the data
		QueryResultDto<FilterFieldQueryItemResultDto> queryResultDto;
		try
		{
			var result = dataSourceEntity.GetElements(query);

			foreach (var element in result)
			{
				element.PrepareValuesForResponse(dataSourceEntity.Fields, queryFields);
			}

			// combine null and empty string values together
			var rows = result
				.GroupBy(element => string.IsNullOrEmpty(element.Values[filterFieldLabel]?.ToString()) ? "-" : element.Values[filterFieldLabel]?.ToString())
				.Select(grouping => grouping.Aggregate(new FilterFieldQueryItemResultDto
				{
					Label = grouping.Key,
					Value = "",
					Count = 0
				}, (itemDto, element) =>
				{
					itemDto.Value = element.Values[filterFieldLabel] ?? "";
					itemDto.Count += (long)(element.Values[countAlias] ?? 0L);
					return itemDto;
				}));

			queryResultDto = new QueryResultDto<FilterFieldQueryItemResultDto>
			{
				Rows = rows.ToList(),
				CountTotal = result.CountTotal
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			queryResultDto = new QueryResultDto<FilterFieldQueryItemResultDto>()
			{
				Rows = [],
				CountTotal = 0
			};
		}

		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Returns the count of elements within a data source.
	/// </summary>
	/// <param name="dataSourceGuid">ID of the used data source configuration</param>
	/// <param name="parameters">an object to query a Subset of cultures</param>
	[HttpGet("/Api/DataSources/{dataSourceGuid:guid}/Elements/Count")]
	public ActionResult<FrontendResponse> Count(Guid dataSourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.AsSplitQuery()
			.FirstOrDefault(dataSource => dataSource.Id == dataSourceGuid);

		if (dataSourceEntity == null)
		{
			throw new ElementNotFoundException($"Source with id: {dataSourceGuid} could not be found");
		}

		const string countAlias = "Count";
		const string countName = "Count(*)";
		var queryFields = new List<DataStoreQueryField> { new(countName, countAlias) };
		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters is { Count: > 0 })
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
			{
				//@TODO Workaround - @Eric - can be removed if prepareForDataStoreQuery is working for this endpoint
				PrepareFrontendFilters(dataSourceEntity, frontendFilters);
				query.WithFilter(frontendFilters);
			}
		}

		// send request to the data store and query the data
		QueryCountDto queryResultDto;
		try
		{
			var result = dataSourceEntity.GetElements(query);
			queryResultDto = new QueryCountDto
			{
				Count = (long)(result.FirstOrDefault()?.Values[countAlias] ?? 0L)
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data count from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			queryResultDto = new QueryCountDto
			{
				Count = 0L
			};
		}

		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Execute an action on a specific record
	/// </summary>
	/// <param name="sourceGuid">ID of the data source configuration</param>
	/// <param name="actionDto">Dto which contains the action</param>
	[HttpPatch("/Api/DataSources/{sourceGuid:guid}/Elements/Action")]
	public async Task<ActionResult<FrontendResponse>> ExecuteAction(Guid sourceGuid, [FromBody] ElementActionDto actionDto)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .FirstOrDefaultAsync(entity => entity.Id == sourceGuid);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {sourceGuid} could not be found");

			if (actionDto.Elements != null)
			{
				var tasks = actionDto.Elements.Select(elementGuid => dataSource.ExecuteElementAction(actionDto, elementGuid.ToString()));
				await Task.WhenAll(tasks);
			}

			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {sourceGuid} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	private void PrepareFrontendFilters(DataSourceEntity dataSourceEntity, QueryFilterGroup frontendFilters)
	{
		frontendFilters.Filters.ForEach(PrepareFilterFields);
		frontendFilters.FilterGroups.ForEach(PrepareFilterGrouping);

		void PrepareFilterGrouping(QueryFilterGroup queryFilterGroup)
		{
			queryFilterGroup.FilterGroups.ForEach(PrepareFilterGrouping);
			queryFilterGroup.Filters.ForEach(PrepareFilterFields);
		}

		void PrepareFilterFields(QueryFilter queryFilter)
		{
			if (queryFilter.SourceValueType is not QueryFilterValueType.Field || queryFilter.SourceValue is null)
				return;
			var filterFieldName = (string)queryFilter.SourceValue;
			var filterFieldSplit = filterFieldName.Split(".");
			var filterByDisplayField = filterFieldSplit.Length == 1 || string.Equals(filterFieldSplit[1], "Display", StringComparison.CurrentCultureIgnoreCase);
			var filterField = dataSourceEntity.Fields.FirstOrDefault(field => field.Name == filterFieldSplit[0]);
			if (filterField == null)
				return;

			switch (filterField.FieldType)
			{
				case DataFieldType.LookupField:
					queryFilter.SourceValue = filterByDisplayField ? $"{filterField.Name}.{filterField.LookupDisplayField?.Name}" : queryFilter.SourceValue;
					break;
				case DataFieldType.VirtualField:
					if (filterField.VirtualDataStoreQueryName == null)
						break;

					if (filterField.Type == DataType.Guid && !filterByDisplayField)
					{
						var queryParts = filterField.VirtualDataStoreQueryName.Split('.');
						queryParts[^1] = "Id";
						queryFilter.SourceValue = string.Join('.', queryParts);
					} else
						queryFilter.SourceValue = filterField.VirtualDataStoreQueryName;
					break;
			}
		}
	}

	private IList<WorkflowInfoDto> GetWorkflowInfos(DataSourceEntity dataSourceEntity, DataStoreElement element, bool includeNodes = false)
	{
		IList<WorkflowInfoDto> workflowInfos = [];
		dataSourceEntity.Workflows.OrderBy(workflow => workflow.Slot).ForEach(workflow =>
		{
			if (!workflow.Enabled)
				return;

			if (workflow.StatusField == null)
				return;

			// Status value might be empty or not a valid Guid -> take start node if that happens
			Guid statusValue;
			try
			{
				statusValue = Guid.Parse((element.Values[workflow.StatusField!.Name] as string)!);
			}
			catch (Exception e)
			{
				if (e is FormatException or ArgumentNullException)
					statusValue = workflow.Nodes.First(node => node.State == WorkflowNodeState.Start).Id;
				else
					throw;
			}
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == statusValue);
			if (node == null)
				return;

			var workflowLocalizer = StringLocalizerFactory.Create($"Workflow", workflow.Id.ToString());
			workflowInfos.Add(new WorkflowInfoDto()
			{
				Slot = workflow.Slot,
				WorkflowId = workflow.Id,
				WorkflowName = workflowLocalizer[workflow.Name],
				NodeId = node.Id,
				NodeName = workflowLocalizer[node.Name],
				NodeIcon = node.Icon,
				State = node.State,
				Nodes = includeNodes ? workflow.Nodes.OrderBy(nodeDefinition => nodeDefinition.Sorting).Select(nodeDefinition => new WorkflowNodeInfoDto()
				{
					Id = nodeDefinition.Id,
					Name = workflowLocalizer[nodeDefinition.Name],
					Icon = nodeDefinition.Icon,
					State = nodeDefinition.State
				}).ToList() : []
			});
		});

		return workflowInfos;
	}

	[GeneratedRegex("##([a-zA-Z0-9_-]+)##")]
	private static partial Regex PlaceholderRegex();

	/// <summary>
	/// Export data to Excel
	/// </summary>
	/// <param name="webSocketManager">WebSocket connection manager</param>
	/// <returns>Excel file as a stream</returns>
	[HttpPost("/Api/Stream/Export/Data")]
	public async Task<IActionResult> ExportExcelData([FromServices] IWebSocketConnectionManager webSocketManager)
	{
		// Get the process ID from the request header
		var processId = Request.Headers["X-Process-ID"].ToString();

		// Check if process ID is provided
		if (string.IsNullOrEmpty(processId))
		{
			// Log the error
			return BadRequest("Process ID is required");
		}

		// Wait briefly for the WebSocket connection to be established
		for (var connectionAttemptIndex = 0; connectionAttemptIndex < 5; connectionAttemptIndex++)
		{
			// Check if the WebSocket connection is established
			if (webSocketManager.HasHandler(processId))
			{
				break;
			}
			// Wait for 300ms
			await Task.Delay(300);
		}

		// Check if the WebSocket connection is established
		if (!webSocketManager.HasHandler(processId))
		{
			// Log the error
			return BadRequest("WebSocket connection not established");
		}

		// Get the WebSocket handler
		var webSocketHandler = webSocketManager.GetHandler(processId);

		try
		{
			// Read the request body only once and store it
			string requestBody;

			// Enable buffering to allow the request body to be read multiple times if needed
			using (var streamReader = new StreamReader(Request.Body))
			{
				// Read the request body
				requestBody = await streamReader.ReadToEndAsync();
			}

			// Deserialize the request body
			var exportRequestData = JsonConvert.DeserializeObject<ExportRequestDto>(requestBody);

			// Check if the request is valid
			if (exportRequestData?.Columns == null || exportRequestData.Columns.Count == 0)
			{
				// Send error message to the client
				await webSocketHandler.SendMessageAsync(new { type = "error", error = "No data found" });
				// Return bad request
				return BadRequest("No data found");
			}

			// Get all field IDs from the request
			var fieldIds = exportRequestData.Columns.Select(column => column.fieldId).ToList();

			// Get the data source ID from the first field
			var dataSourceId = _databaseContext.DataFields.First(field => fieldIds.Contains(field.Id)).DataSourceId;

			// Get the data source
			var dataSource = _databaseContext.DataSources
				.Include(dataSource => dataSource.Fields)
				.FirstOrDefault(dataSource => dataSource.Id == dataSourceId);

			// Check if the data source exists
			if (dataSource == null)
			{
				// Send error message to the client
				throw new ElementNotFoundException($"DataSource with id: {dataSourceId} could not be found");
			}

			// Get all fields
			var queryFields = exportRequestData.Columns
				.Select(column => new DataStoreQueryField(column.display))
				.ToList();

			// Create query
			var dataStoreQuery = new DataStoreQuery(queryFields);
			// Prepare query for data store
			dataStoreQuery.PrepareQueryForDataStore(dataSource.Fields);

			// Add filters
			if (exportRequestData.SelectedIds != null && exportRequestData.SelectedIds.Count > 0)
			{
				// Create filter group
				var filterGroup = new QueryFilterGroup();
				// Add filter
				filterGroup.AddFilter(new InFilter(new QueryFilterField("Id"), exportRequestData.SelectedIds));
				// Add filter group to query
				dataStoreQuery.WithFilter(filterGroup);
			}

			// Get total count
			var queryResult = dataSource.GetElements(dataStoreQuery);
			// Get total count
			var totalEntries = queryResult.Count();

			// Send initial progress
			await webSocketHandler.SendMessageAsync(new
			{
				type = "start",
				total = totalEntries,
				message = "Starting export process"
			});

			// Create Excel workbook
			using var excelWorkbook = new XLWorkbook();
			// Add worksheet
			var excelWorksheet = excelWorkbook.Worksheets.Add("Data");

			// Add headers
			for (int columnIndex = 0; columnIndex < queryFields.Count; columnIndex++)
			{
				// Get localized header name
				excelWorksheet.Cell(1, columnIndex + 1).Value = GetLocalizedHeaderName(queryFields[columnIndex].Name);
			}

			// Add data rows with progress updates
			int currentRowIndex = 2;
			int processedRecordsCount = 0;
			int batchSize = 100; // Process in batches to prevent timeouts
			int currentBatchNumber = 0;

			// Process in batches
			foreach (var elementBatch in queryResult.Chunk(batchSize))
			{
				// Process each element in the batch
				foreach (var dataElement in elementBatch)
				{
					// Prepare values for response
					dataElement.PrepareValuesForResponse(dataSource.Fields, queryFields);

					// Add data row
					for (int columnIndex = 0; columnIndex < queryFields.Count; columnIndex++)
					{
						// Get field
						var dataField = dataSource.Fields.First(field => field.Name == queryFields[columnIndex].Name);
						// Get value
						object? cellValue;
						// ReSharper disable once PossibleMultipleEnumeration
						dataElement.Values.TryGetValue(queryFields[columnIndex].Name, out cellValue);

						// Get cell
						var excelCell = excelWorksheet.Cell(currentRowIndex, columnIndex + 1);
						// Set value
						if (cellValue != null)
						{
							// Convert value based on data type
							switch (dataField.Type)
							{
								case DataType.Integer:
									if (int.TryParse(cellValue.ToString(), out int integerValue))
										excelCell.SetValue(integerValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Long:
									if (long.TryParse(cellValue.ToString(), out long longValue))
										excelCell.SetValue(longValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Double:
									if (double.TryParse(cellValue.ToString(), out double doubleValue))
										excelCell.SetValue(doubleValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Date:
									if (DateTime.TryParse(cellValue.ToString(), out DateTime dateValue))
									{
										excelCell.SetValue(dateValue.Date);
										excelCell.Style.DateFormat.Format = "yyyy-MM-dd";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.DateTime:
									if (DateTime.TryParse(cellValue.ToString(), out DateTime dateTimeValue))
									{
										excelCell.SetValue(dateTimeValue);
										excelCell.Style.DateFormat.Format = "yyyy-MM-dd HH:mm:ss";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Time:
									if (TimeSpan.TryParse(cellValue.ToString(), out TimeSpan timeValue))
									{
										excelCell.SetValue(timeValue.ToString(@"HH\:mm\:ss"));
										excelCell.Style.DateFormat.Format = "HH:mm:ss";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Boolean:
									if (bool.TryParse(cellValue.ToString(), out bool booleanValue))
										excelCell.SetValue(booleanValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.String:
								case DataType.Text:
								default:
									excelCell.SetValue(cellValue.ToString() ?? "");
									break;
							}
						}
						else
						{
							excelCell.SetValue("");
						}
					}
					currentRowIndex++;
					processedRecordsCount++;
				}

				// Update progress after each batch
				currentBatchNumber++;
				await webSocketHandler.SendProgressUpdate(
					processedRecordsCount,
					totalEntries,
					$"Processing batch {currentBatchNumber} of {(totalEntries + batchSize - 1) / batchSize}"
				);

				// Small delay between batches to prevent overwhelming the connection
				await Task.Delay(100);
			}

			// Format headers and adjust columns
			var headerRow = excelWorksheet.Row(1);
			// Make headers bold
			headerRow.Style.Font.Bold = true;

			// Apply column formatting
			for (int columnIndex = 0; columnIndex < queryFields.Count; columnIndex++)
			{
				var dataField = dataSource.Fields.First(field => field.Name == queryFields[columnIndex].Name);
				var excelColumn = excelWorksheet.Column(columnIndex + 1);

				// Apply column formatting
				switch (dataField.Type)
				{
					case DataType.Integer:
					case DataType.Long:
						excelColumn.Style.NumberFormat.Format = "#,##0";
						break;
					case DataType.Double:
						excelColumn.Style.NumberFormat.Format = dataField.DecimalPlaces > 0 ?
							$"#,##0.{new string('0', dataField.DecimalPlaces)}" : "#,##0";
						break;
					case DataType.Date:
						excelColumn.Style.DateFormat.Format = "yyyy-MM-dd";
						break;
					case DataType.DateTime:
						excelColumn.Style.DateFormat.Format = "yyyy-MM-dd HH:mm:ss";
						break;
					case DataType.Time:
						excelColumn.Style.DateFormat.Format = "HH:mm:ss";
						break;
				}
			}

			// Adjust column widths
			excelWorksheet.Columns().AdjustToContents();

			// Convert to bytes
			using var memoryStream = new MemoryStream();
			// Save workbook
			excelWorkbook.SaveAs(memoryStream);
			// Get bytes
			var excelFileBytes = memoryStream.ToArray();

			// Create file name
			var exportFileName = $"{dataSource.Name}_export_{DateTime.Now:yyyy-MM-dd}.xlsx";

			// Add metadata headers
			Response.Headers.Append("X-Total-Records", queryResult.Count().ToString());
			Response.Headers.Append("X-Columns-Count", queryFields.Count.ToString());
			Response.Headers.Append("Content-Length", excelFileBytes.Length.ToString());

			// Send completion message
			await webSocketHandler.SendMessageAsync(new
			{
				type = "complete",
				message = "Export completed successfully"
			});

			// Return file
			return File(excelFileBytes,
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				exportFileName);
		}
		catch (Exception exportException)
		{
			// Log the error
			Logger.Error(exportException, "Error during export process");
			// Send error message
			await webSocketHandler.SendMessageAsync(new
			{
				type = "error",
				error = "Export failed: " + exportException.Message
			});
			// Return error
			return StatusCode(400, new { error = "Export failed", message = exportException.Message });
		}
	}

	/// <summary>
	/// Returns a list of available columns for a data source.
	/// </summary>
	/// <param name="pageViewGuid">ID of the page view</param>
	[HttpGet("/Api/PageViews/{pageViewGuid:guid}/Columns")]
	public ActionResult<FrontendResponse> GetPageViewColumns(Guid pageViewGuid)
	{
		// Get all columns for the given page view
		var listViewColumns = _databaseContext.ListViewColumns
			.Include(listViewColumn => listViewColumn.Field)
			.Where(listViewColumn =>
				listViewColumn.ListViewId == pageViewGuid &&
				listViewColumn.Field != null)
			.OrderBy(listViewColumn => listViewColumn.Position)
			.ToList();

		// Check if there are any columns
		if (!listViewColumns.Any())
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get the first column to find the DataSourceId
		var firstListViewColumn = listViewColumns.FirstOrDefault();
		// Check if the first column exists and has a Field
		if (firstListViewColumn?.Field == null)
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get the DataSourceId from the first column's Field
		var dataSourceId = firstListViewColumn.Field.DataSourceId;
		// Check if the DataSourceId exists
		if (dataSourceId == null)
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get all visible columns
		var visibleColumnsList = listViewColumns
			.Select(listViewColumn => new PresetColumnInfoDto
			{
				Key = listViewColumn.Field!.Name.ToLower(),
				Display = !string.IsNullOrEmpty(listViewColumn.Label)
					? listViewColumn.Label
					: FormatDisplayName(listViewColumn.Field.Name),
				FieldType = listViewColumn.Field.Type.ToString(),
				FieldId = listViewColumn.Field.Id
			})
			.ToList();

		// Get all invisible columns from the same data source
		var invisibleColumnsList = _databaseContext.DataFields
			.Where(dataField =>
				dataField.DataSourceId == dataSourceId &&
				dataField.SystemField == false &&
				!listViewColumns.Select(listViewColumn => listViewColumn.FieldId).Contains(dataField.Id))
			.Select(dataField => new PresetColumnInfoDto
			{
				Key = dataField.Name.ToLower(),
				Display = dataField.Name,
				FieldType = dataField.Type.ToString(),
				FieldId = dataField.Id
			})
			.ToList();

		// Create the result
		var columnsResult = new PresetColumnsResultDto
		{
			visibleColumns = visibleColumnsList,
			InvisibleColumns = invisibleColumnsList,
			DataSourceId = dataSourceId
		};

		// Return the result
		return GetOkResponse(columnsResult);
	}

	/// <summary>
	/// Import data from Excel file
	/// </summary>
	/// <param name="webSocketManager">WebSocket connection manager</param>
	/// <returns>Import result with summary and any errors</returns>
	[HttpPost("/Api/PageViews/ExcelImport")]
	public async Task<ActionResult<FrontendResponse>> ImportExcelData([FromServices] IWebSocketConnectionManager webSocketManager)
	{
		// Get the process ID from the request header
		var processId = Request.Headers["X-Process-ID"].ToString();
		// Check if process ID is provided
		if (string.IsNullOrEmpty(processId))
		{
			// Log the error
			return BadRequest("Process ID is required");
		}

		// Wait briefly for the WebSocket connection to be established
		for (int connectionAttemptIndex = 0; connectionAttemptIndex < 5; connectionAttemptIndex++)
		{
			// Check if the WebSocket connection is established
			if (webSocketManager.HasHandler(processId))
			{
				// Connection established
				break;
			}
			// Wait for 300ms
			await Task.Delay(300);
		}

		// Check if the WebSocket connection is established
		if (!webSocketManager.HasHandler(processId))
		{
			// Log the error
			return BadRequest("WebSocket connection not established");
		}

		// Get the WebSocket handler
		var webSocketHandler = webSocketManager.GetHandler(processId);

		try
		{
			// Get the import parameters from the request
			var importParametersJson = Request.Form["importParams"].ToString();
			// Deserialize the import parameters
			var importParameters = JsonConvert.DeserializeObject<ExcelImportParametersDto>(importParametersJson);

			// Check if the import parameters are valid
			if (importParameters == null)
			{
				// Log the error
				return BadRequest("Invalid import parameters");
			}

			// Get the data source
			var dataSource = await _databaseContext.DataSources
								 .Include(dataSource => dataSource.Fields).ThenInclude(dataFieldEntity => dataFieldEntity.LookupDisplayField!)
								 .FirstOrDefaultAsync(dataSource => dataSource.Id == importParameters.DataSourceId);

			// Check if the data source exists
			if (dataSource == null)
			{
				// Log the error
				throw new ElementNotFoundException($"DataSource with id: {importParameters.DataSourceId} could not be found");
			}

			// Get the Excel file from the request
			var uploadedFile = Request.Form.Files.FirstOrDefault();
			// Check if the file exists
			if (uploadedFile == null || uploadedFile.Length == 0)
			{
				// Log the error
				return BadRequest("No file uploaded");
			}



			byte[] fileContentBytes;
			// Read the file content as a byte array for storage in the database
			using (var fileMemoryStream = new MemoryStream())
			{
				// Copy the file content to the memory stream
				uploadedFile.OpenReadStream().CopyTo(fileMemoryStream);
				// Get the byte array from the memory stream
				fileContentBytes = fileMemoryStream.ToArray();
			}

			// Save file details to the database using FileUploadDto
			var currentUser = await _userManager.GetCurrentUserAsync();
			// Create the FileUploadDto
			var fileUploadDto = new FileUploadDto
			{
				DataSourceId = dataSource.Id,
				FileName = uploadedFile.FileName,
				IsPublic = false,
				IsReadOnly = true,
				IsDeleted = false,
				Created = DateTime.Now,
				UploadedBy = currentUser.DisplayName,
				LastModified = DateTime.Now,
				LastModifiedBy = currentUser.DisplayName,
				Revision = Guid.NewGuid()
			};

			// Create the FileUploadEntity and save it to the database
			var fileUploadEntity = FileUploadEntity.FromDto(fileUploadDto, currentUser, _databaseContext);
			// Set the file content directly in the entity
			fileUploadEntity.File = fileContentBytes;
			// Initialize import statistics and status
			fileUploadEntity.Status = "Import in progress";
			// Add the entity to the context
			_databaseContext.FileUploads.Add(fileUploadEntity);

			try
			{
				// Save changes to the database and ensure the file is properly saved before proceeding
				await _databaseContext.SaveChangesAsync();

				// Only proceed with Excel processing if the file was successfully saved
				Logger.Information("File successfully saved to database with ID: {FileEntityId}", fileUploadEntity.Id);

				// Read the Excel file
				using var fileStream = uploadedFile.OpenReadStream();
				// Create the workbook
				using var excelWorkbook = new XLWorkbook(fileStream);
				// Get the first worksheet
				var excelWorksheet = excelWorkbook.Worksheet(1); // Get the first worksheet

				// Check if the worksheet exists and has data
				if (excelWorksheet == null || excelWorksheet.LastRowUsed() == null)
				{
					return BadRequest("Excel file is empty or has invalid format");
				}

				// Get the header row
				var headerRow = excelWorksheet.Row(1);
				// Get all headers
				var excelHeaders = new List<string>();
				// Get the last cell used in the header row
				var lastUsedCell = headerRow.LastCellUsed();
				// Check if there are any headers
				if (lastUsedCell != null)
				{
					// Get all headers
					for (int columnIndex = 1; columnIndex <= lastUsedCell.Address.ColumnNumber; columnIndex++)
					{
						// Add the header to the list
						excelHeaders.Add(headerRow.Cell(columnIndex).Value.ToString());
					}
				}

				// Map Excel columns to data fields
				var excelColumnMappings = new List<ExcelColumnMappingDto>();
				// Loop through all selected mappings
				foreach (var selectedMapping in importParameters.SelectedMappings)
				{
					// Excel columns are 1-based, so add 1 to the index
					var excelColumnIndex = selectedMapping.index + 1;

					// Check if the Excel column index is valid
					if (excelColumnIndex > 0 && excelColumnIndex <= excelHeaders.Count)
					{
						// Get the header name for the Excel column
						var headerName = excelHeaders[excelColumnIndex - 1];
						// Add the mapping
						excelColumnMappings.Add(selectedMapping);
					}
				}

				// Process data rows
				var importResult = new ExcelRecordDto();
				// Get the last row
				var lastUsedRow = excelWorksheet.LastRowUsed();
				// Check if there are any rows
				if (lastUsedRow == null)
				{
					return BadRequest("Excel file is empty or has invalid format");
				}
				// Get the row count
				var totalRowCount = lastUsedRow.RowNumber();
				// Get the start row
				var startRowIndex = importParameters.IsFirstLineHeader ? 2 : 1;
				var processedRowsCount = 0;
				// Get the current user
				var currentUserEntity = await _userManager.GetCurrentUserAsync();

				// Send initial progress
				await webSocketHandler.SendMessageAsync(new
				{
					type = "start",
					total = totalRowCount - startRowIndex + 1,
					message = "Starting import process"
				});

				// Process each row
				for (int currentRowIndex = startRowIndex; currentRowIndex <= totalRowCount; currentRowIndex++)
				{
					// Create a dictionary to store the row data
					Dictionary<string, object?> currentRowData = new();
					// Keep track of the last processed column and value for error reporting
					string? lastProcessedColumnKey = null;
					// Keep track of the last processed value for error reporting
					string? lastProcessedCellValue = null;

					try
					{
						// Process each column mapping
						foreach (var columnMapping in excelColumnMappings)
						{
							// Excel columns are 1-based, so add 1 to the index
							var excelColumnIndex = columnMapping.index + 1;
							// Get the cell
							var excelCell = excelWorksheet.Cell(currentRowIndex, excelColumnIndex);
							// Get the value
							var cellValue = excelCell.Value.ToString();
							// Update the last processed column and value
							lastProcessedColumnKey = columnMapping.ColumnKey;
							// Update the last processed value
							lastProcessedCellValue = cellValue;

							try
							{
								// Convert the value based on the data type
								object? convertedValue = columnMapping.DataType switch
								{
									"Integer" => int.TryParse(cellValue, out var integerValue) ? integerValue : null,
									"Long" => long.TryParse(cellValue, out var longValue) ? longValue : null,
									"Double" => double.TryParse(cellValue, out var doubleValue) ? doubleValue : null,
									"Date" => DateTime.TryParse(cellValue, out var dateValue) ? dateValue.Date : null,
									"DateTime" => DateTime.TryParse(cellValue, out var dateTimeValue) ? dateTimeValue : null,
									"Time" => TimeSpan.TryParse(cellValue, out var timeValue) ? timeValue : null,
									"Boolean" => bool.TryParse(cellValue, out var booleanValue) ? booleanValue : null,
									_ => string.IsNullOrWhiteSpace(cellValue) ? null : cellValue
								};

								// Add the converted value to the row data
								if (convertedValue != null)
								{
									// Add the converted value to the row data
									currentRowData[columnMapping.ColumnKey] = convertedValue;
								}
							}
							catch (Exception conversionException)
							{
								// Log the error
								importResult.Summary.Failed++;
								// Add the error to the result
								importResult.Errors.Add(new ExcelImportError
								{
									RowNumber = currentRowIndex,
									Error = conversionException.Message.Replace("'", ""),
									ColumnKey = columnMapping.ColumnKey,
									Value = cellValue
								});
								throw; // Re-throw to skip this row
							}
						}

						// Create element data
						var dataStoreElementData = new DataStoreElementData(currentRowData, new List<string> { "testgroup" });

						// Validate that we have data to insert
						if (currentRowData.Count == 0)
						{
							// Log the error
							importResult.Summary.Failed++;
							// Add the error to the result
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Row contains no data to import"
							});
							continue;
						}

						// Validate that we have at least one non-null value
						if (!currentRowData.Any(keyValuePair => keyValuePair.Value != null))
						{
							// Log the error
							importResult.Summary.Failed++;
							// Add the error to the result
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Row contains only null values"
							});
							continue;
						}

						// Remove virtual fields as they are handled differently
						var virtualFieldNames = dataSource.Fields
							.Where(dataField => dataField.FieldType == DataFieldType.VirtualField)
							.Select(dataField => dataField.Name)
							.ToList();

						// Remove virtual fields from the element data
						foreach (var virtualFieldName in virtualFieldNames)
						{
							// ReSharper disable once PossibleNullReferenceException
							dataStoreElementData.Values.Remove(virtualFieldName);
						}

						// Prepare values for data store
						dataStoreElementData.PrepareValuesForDataStore(dataSource.Fields);

						// Always check for duplicates
						// Get unique set columns from the data source
						var uniqueSetColumns = _databaseContext.UniqueSetColumns
							.Include(uniqueSetColumn => uniqueSetColumn.DataField)
							.Where(uniqueSetColumn => uniqueSetColumn.DataSourceId == dataSource.Id)
							.Select(uniqueSetColumn => uniqueSetColumn.ColumnName)
							.ToList();

						// Get the values to match on
						var duplicateMatchValues = uniqueSetColumns
							.Where(columnKey => currentRowData.ContainsKey(columnKey) && currentRowData[columnKey] != null)
							.ToDictionary(columnKey => columnKey, columnKey => currentRowData[columnKey]);

						// Check for duplicates
						if (duplicateMatchValues.Any())
						{
							// Create query for duplicate check
							var duplicateCheckQuery = new DataStoreQuery(new List<DataStoreQueryField> { new("Id") });
							var duplicateFilterGroup = new QueryFilterGroup();

							// Add filters
							foreach (var (columnKey, columnValue) in duplicateMatchValues)
							{
								if (columnValue != null)
								{
									duplicateFilterGroup.AddFilter(new EqualsFilter(new QueryFilterField(columnKey), columnValue));
								}
							}

							// Add filter group to query
							duplicateCheckQuery.WithFilter(duplicateFilterGroup);
							var existingElements = dataSource.GetElements(duplicateCheckQuery);

							// Check if we have any duplicates
							if (existingElements.Any())
							{
								// Get the first existing element
								var existingElement = existingElements.First();

								switch (importParameters.ActionForDuplicates?.ToLower())
								{
									case "skip":
										importResult.Summary.Skipped++;
										continue;

									case "update":
										var updateElementData = new DataStoreElementData(existingElement.Id, currentRowData, fileUploadId: null);
										updateElementData.PrepareValuesForDataStore(dataSource.Fields);

										var operationOrigin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1,  currentUserEntity.DisplayName);
										var lookupDisplayQueryFields = dataSource.Fields.Where(dataField => dataField is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
											.Select(dataField => new DataStoreQueryField(dataField.Name + "." + dataField.LookupDisplayField!.Name, dataField.Name + ".Display"))
											.ToList();

										var elementUpdateResult = dataSource.UpdateElement(updateElementData, operationOrigin, lookupDisplayQueryFields);

										if (elementUpdateResult.ElementData != null)
										{
											importResult.Summary.Updated++;
										}
										else
										{
											Logger.Warning("Failed to update record with id {Id} in data source {DataSourceName}",
												existingElement.Id, dataSource.Name);
											importResult.Summary.Failed++;
										}
										continue;

									default: // "PerElement" or any other value
										importResult.Duplicates.Add(new ExcelDuplicateRecordDto
										{
											Record = currentRowData,
											RowIndex = currentRowIndex,
											ColumnKey = duplicateMatchValues.First().Key,
											Value = duplicateMatchValues.First().Value?.ToString(),
											Id = Guid.Parse(existingElement.Id)
										});
										importResult.Summary.Skipped++;
										continue;
								}
							}
						}

						// Create new record
						var elementCreateResult = dataSource.CreateElement(dataStoreElementData,
							new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, currentUserEntity.DisplayName));

						// Check if the create was successful
						if (elementCreateResult.ElementData != null)
						{
							// Update the summary
							importResult.Summary.Inserted++;
						}
						else
						{
							importResult.Summary.Failed++;
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Failed to create record"
							});
						}
					}
					catch (Exception rowProcessingException)
					{
						importResult.Summary.Failed++;
						importResult.Errors.Add(new ExcelImportError
						{
							RowNumber = currentRowIndex,
							Error = rowProcessingException.Message.Replace("'", ""),
							ColumnKey = lastProcessedColumnKey,
							Value = lastProcessedCellValue
						});
					}

					processedRowsCount++;
					// Update progress every 10 rows
					if (processedRowsCount % 10 == 0) // Update progress every 10 rows
					{
						await webSocketHandler.SendProgressUpdate(
							processedRowsCount,
							totalRowCount - startRowIndex + 1,
							$"Processing row {currentRowIndex} of {totalRowCount}"
						);
					}
				}

				// Include the entity ID in the response
				importResult.FileEntityId = fileUploadEntity.Id.ToString();

				// Update the file upload entity with final statistics
				fileUploadEntity.Updated = importResult.Summary.Updated;
				fileUploadEntity.Imported = importResult.Summary.Inserted;
				fileUploadEntity.Skipped = importResult.Summary.Skipped;
				fileUploadEntity.Failed = importResult.Summary.Failed;
				fileUploadEntity.Status = importResult.Summary.Failed > 0
					? "Completed with errors"
					: "Completed successfully";

				// Save the updated entity
				await _databaseContext.SaveChangesAsync();
				Logger.Information("Updated file upload entity with final statistics: Updated={Updated}, Imported={Imported}, Skipped={Skipped}, Failed={Failed}, Status={Status}",
					fileUploadEntity.Updated, fileUploadEntity.Imported, fileUploadEntity.Skipped, fileUploadEntity.Failed, fileUploadEntity.Status);

				// Generate Excel file with errors if there are any
				if (importResult.Errors.Count > 0)
				{
					// Create Excel workbook for errors
					using var errorExcelWorkbook = new XLWorkbook();
					var errorExcelWorksheet = errorExcelWorkbook.Worksheets.Add("Import Errors");

					// Add headers
					errorExcelWorksheet.Cell(1, 2).Value = "Element";
					errorExcelWorksheet.Cell(1, 1).Value = "Line Number";
					errorExcelWorksheet.Cell(1, 3).Value = "Value";
					errorExcelWorksheet.Cell(1, 4).Value = "Error Description";

					// Format headers
					var errorHeaderRow = errorExcelWorksheet.Row(1);
					errorHeaderRow.Style.Font.Bold = true;

					// Add data rows
					var errorRowIndex = 2;
					foreach (var importError in importResult.Errors)
					{
						errorExcelWorksheet.Cell(errorRowIndex, 2).Value = importError.ColumnKey ?? string.Empty;
						errorExcelWorksheet.Cell(errorRowIndex, 1).Value = importError.RowNumber;
						errorExcelWorksheet.Cell(errorRowIndex, 3).Value = importError.Value ?? string.Empty;
						errorExcelWorksheet.Cell(errorRowIndex, 4).Value = importError.Error;
						errorRowIndex++;
					}

					// Adjust column widths
					errorExcelWorksheet.Columns().AdjustToContents();

					// Convert to bytes
					using var errorMemoryStream = new MemoryStream();
					errorExcelWorkbook.SaveAs(errorMemoryStream);
					var errorFileBytes = errorMemoryStream.ToArray();

					// Convert to Base64 for sending in the response
					importResult.ErrorsExcelBase64 = Convert.ToBase64String(errorFileBytes);
				}

				// Send completion message
				await webSocketHandler.SendMessageAsync(new
				{
					type = "complete",
					message = "Import completed successfully",
					result = importResult,
					fileEntityId = fileUploadEntity.Id.ToString()
				});

				return GetOkResponse(importResult);
			}
			catch (Exception importProcessingException)
			{
				Logger.Error(importProcessingException, "Error during import process");

				// Update the fileUploadEntity with error status
				fileUploadEntity.Status = "Failed: " + importProcessingException.Message;
				await _databaseContext.SaveChangesAsync();
				Logger.Information("Updated file upload entity with error status: {Status}", fileUploadEntity.Status);

				await webSocketHandler.SendMessageAsync(new
				{
					type = "error",
					error = "Import failed: " + importProcessingException.Message
				});
				return StatusCode(400, new { error = "Import failed", message = importProcessingException.Message });
			}
		}
		catch (Exception generalImportException)
		{
			Logger.Error(generalImportException, "Error during import process");

			await webSocketHandler.SendMessageAsync(new
			{
				type = "error",
				error = "Import failed: " + generalImportException.Message
			});
			return StatusCode(400, new { error = "Import failed", message = generalImportException.Message });
		}
	}

	/// <summary>
	/// Updates duplicate records from Excel import
	/// </summary>
	/// <param name="webSocketManager">WebSocket connection manager</param>
	/// <param name="updateDuplicatesData">DTO containing the duplicates to update</param>
	[HttpPost("/Api/PageViews/ExcelImport/UpdateDuplicate")]
	public async Task<ActionResult<FrontendResponse>> UpdateDuplicates([FromServices] IWebSocketConnectionManager webSocketManager, [FromBody] ExcelUpdateDuplicatesDto updateDuplicatesData)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
				.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.LookupDisplayField)
				.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.VirtualLookupField)
				.FirstOrDefaultAsync(dataSourceEntity => dataSourceEntity.Id == updateDuplicatesData.DataSourceId);

			_ = dataSource ?? throw new ElementNotFoundException($"DataSource with id: {updateDuplicatesData.DataSourceId} could not be found");

			var updateResult = new ExcelUpdateDuplicatesResultDto
			{
				Summary = new ExcelImportSummaryDto()
			};

			// Get the file upload entity if fileEntityId is provided
			FileUploadEntity? fileUploadEntity = null;
			if (!string.IsNullOrEmpty(updateDuplicatesData.FileEntityId))
			{
				fileUploadEntity = await _databaseContext.FileUploads.FindAsync(Guid.Parse(updateDuplicatesData.FileEntityId));
			}

			foreach (var duplicateRecord in updateDuplicatesData.Duplicates)
			{
				if (duplicateRecord.Id == null)
				{
					updateResult.Summary.Skipped++;
					continue;
				}

				var existingDataElement = dataSource.GetElement(duplicateRecord.Id.Value.ToString());
				if (existingDataElement == null)
				{
					Logger.Warning("Record with id {Id} not found in data source {DataSourceName}",
						duplicateRecord.Id, dataSource.Name);
					updateResult.Summary.Failed++;
					continue;
				}

				var duplicateRowData = duplicateRecord.Record;
				var updateElementData = new DataStoreElementData(existingDataElement.Id, duplicateRowData, fileUploadId: null);
				var currentUser = await _userManager.GetCurrentUserAsync();
				updateElementData.PrepareValuesForDataStore(dataSource.Fields);

				var operationOrigin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, currentUser.DisplayName);
				var lookupDisplayQueryFields = dataSource.Fields.Where(dataField => dataField is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
					.Select(dataField => new DataStoreQueryField(dataField.Name + "." + dataField.LookupDisplayField!.Name, dataField.Name + ".Display"))
					.ToList();

				var elementUpdateResult = dataSource.UpdateElement(updateElementData, operationOrigin, lookupDisplayQueryFields);

				if (elementUpdateResult.ElementData != null)
				{
					updateResult.Summary.Updated++;
					// If we have a file entity, update its statistics
					if (fileUploadEntity != null)
					{
						fileUploadEntity.Skipped--; // Reduce skipped count as we're processing this item
						fileUploadEntity.Updated++; // Increase updated count
					}
				}
				else
				{
					Logger.Warning("Failed to update record with id {Id} in data source {DataSourceName}",
						duplicateRecord.Id, dataSource.Name);
					updateResult.Summary.Failed++;
					// If we have a file entity, update its statistics
					if (fileUploadEntity != null)
					{
						fileUploadEntity.Skipped--; // Reduce skipped count as we're processing this item
						fileUploadEntity.Failed++; // Increase failed count
					}
				}
			}

			// If we have a file entity, update its status and save changes
			if (fileUploadEntity != null)
			{
				fileUploadEntity.Status = updateResult.Summary.Failed > 0
					? "Completed with errors"
					: "Completed successfully";
				await _databaseContext.SaveChangesAsync();
			}

			return GetOkResponse(updateResult);
		}
		catch (Exception updateException)
		{
			Logger.Error(updateException, "Error during update process");
			return StatusCode(400, new { error = "Update failed", message = updateException.Message });
		}
	}

	private string GetLocalizedHeaderName(string name)
	{
		if (string.IsNullOrEmpty(name))
			return string.Empty;

		// Create a localizer for Excel headers
		var excelLocalizer = StringLocalizerFactory.Create("ExcelExport", "");

		// Try to get localized value first
		var localizedValue = excelLocalizer[name];

		// If the localized value is the same as the key, it means no translation was found
		// In that case, fall back to the formatted name
		if (localizedValue == name)
		{
			return FormatHeaderName(name);
		}

		return localizedValue;
	}

	private static string FormatHeaderName(string name)
	{
		if (string.IsNullOrEmpty(name))
			return string.Empty;

		// Split by dots for nested properties (e.g., "User.Name")
		var parts = name.Split('.');

		// Format each part
		var formattedParts = parts.Select(part =>
		{
			// Replace underscores with spaces
			part = part.Replace('_', ' ');

			// Capitalize first letter of each word
			return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(part.ToLower());
		});

		// Join back with dots
		return string.Join(".", formattedParts);
	}

	private static string FormatDisplayName(string name)
	{
		if (string.IsNullOrEmpty(name))
			return string.Empty;

		return char.ToUpper(name[0]) + name.Substring(1).Replace("_", " ");
	}

}
