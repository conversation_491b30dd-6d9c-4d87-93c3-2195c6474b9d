using System.Collections;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Domain.Storage.Db.QueryParser;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using SqlKata;
using Column = SqlKata.Column;

namespace Levelbuild.Domain.Storage.Db;

/// <summary>
/// Parses the Query
/// </summary>
public class FilterParser
{
	public const int MaxDatabaseObjectLength = 63;
	private readonly Func<string, StorageIndexDefinition?> _getStorageIndexDefinition;
	private readonly string _lang;
	private readonly string _defaultLang;
	private readonly bool _ignoreAcls;
	private readonly Db _db;

	/// <summary>
	/// 
	/// </summary>
	/// <param name="db"></param>
	/// <param name="getStorageIndexDefinition"></param>
	/// <param name="lang"></param>
	/// <param name="defaultLang"></param>
	/// <param name="ignoreAcls"></param>
	public FilterParser(Db db, Func<string, StorageIndexDefinition?> getStorageIndexDefinition, string lang, string defaultLang, bool ignoreAcls = false)
	{
		_db = db;
		_getStorageIndexDefinition = getStorageIndexDefinition;
		_lang = lang;
		_defaultLang = defaultLang;
		_ignoreAcls = ignoreAcls;
	}


	/// <summary>
	/// Returns a that can be sent to the database as is based on the dataStoreQuery.
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="dataStoreQuery"></param>
	/// <param name="usedDefinitions"></param>
	/// <param name="changedFieldAliases">A dictionary containing all fields whose aliases would have gotten above the limitation of 63 characters. The key is the new alias, the value is the old alias</param>
	/// <param name="countOnly"></param>
	/// <param name="withDeleted"></param>
	/// <param name="withInactive"></param>
	/// <param name="withHidden"></param>
	/// <returns></returns>
	public Query ParseQuery(StorageIndexDefinition sid, DataStoreQuery dataStoreQuery, out List<string> usedDefinitions,
							out Dictionary<string, string> changedFieldAliases, bool countOnly = false, bool withDeleted = false, bool withInactive = false,
							bool withHidden = false)
	{
		changedFieldAliases = new();
		var fieldParser = new FieldParser(_db, _getStorageIndexDefinition, _lang, _defaultLang, sid, changedFieldAliases);

		Dictionary<string, string> tableDict = new();

		Query query = new(dataStoreQuery.DataSourceName);

		if (!withDeleted)
			query.WhereRaw($"[{dataStoreQuery.DataSourceName}].[{StorageSystemField.SysIsDeleted}] = ?", false);

		IList<DataStoreQueryField> queryFields = new List<DataStoreQueryField>();
		if (!countOnly)
			queryFields = dataStoreQuery.Fields is { Count: > 0 }
							  ? dataStoreQuery.Fields.Distinct().ToList()
							  : sid.Fields.Where(it => _db.InlineMultiValueFields || !it.MultiValue)
								  .Select(it => new DataStoreQueryField(it.Name)).ToList();

		var selectedFields = new List<QueryField>();
		foreach (var field in queryFields)
		{
			var queryField = fieldParser.Parse(field, true);
			AddJoins(query, queryField, tableDict);

			// favourites cannot be selected externally
			if (field.Name != StorageSystemField.SysFavourites.ToString() || withHidden)
			{
				selectedFields.Add(queryField.Clone());
				query.SelectRaw(queryField.AsQueryString());
			}
		}

		selectedFields.ForEach(it => it.WithAlias = false);

		// needed to set inactive fields flag in StorageConnection
		if ((!countOnly && sid is not RevisionDefinition && dataStoreQuery.GroupBy == null && selectedFields.All(it => it.AggregateFunction == null))
			&& (selectedFields.Where(it => it.Name == StorageSystemField.SysInactiveDate.ToString()).FirstOrDefault() == null))
			AddIsInactiveField(sid, query);

		// Add boolean IsFavourite field
		if (!countOnly && sid is not RevisionDefinition && dataStoreQuery.GroupBy == null && selectedFields.All(it => it.AggregateFunction == null))
			AddIsFavouriteField(sid, query);

		List<string> filterFieldNames = new List<string>();
		GetFilterFieldNames(filterFieldNames, dataStoreQuery.Filter);
		var filterFields = new Dictionary<string, QueryField>();
		foreach (var dataStoreQueryField in filterFieldNames.Distinct().Select(it => new DataStoreQueryField(it)).ToList())
		{
			var queryField = fieldParser.Parse(dataStoreQueryField);
			filterFields[dataStoreQueryField.Name] = queryField;
			AddJoins(query, queryField, tableDict);
		}

		query.Where(it => ParseFilterGroup(sid, it, dataStoreQuery.Filter, filterFields, tableDict, selectedFields));

		var groupByFieldNames = new List<string>();
		var orderByFieldNames = new List<string>();
		if (dataStoreQuery.GroupBy != null)
			groupByFieldNames.AddRange(dataStoreQuery.GroupBy);
		if (!countOnly && dataStoreQuery.OrderBy != null)
			orderByFieldNames.AddRange(dataStoreQuery.OrderBy.Select(it => it.Name));
		var groupOrderByFields = new Dictionary<string, QueryField>();
		foreach (var dataStoreQueryField in groupByFieldNames.Distinct().Select(it => new DataStoreQueryField(it)).ToList())
		{
			var queryField = fieldParser.Parse(dataStoreQueryField, allowAggregateFunctions: false);
			groupOrderByFields[dataStoreQueryField.Name] = queryField;
			AddJoins(query, queryField, tableDict);
		}

		foreach (var dataStoreQueryField in orderByFieldNames.Distinct().Select(it => new DataStoreQueryField(it)).ToList())
		{
			var queryField = fieldParser.Parse(dataStoreQueryField);
			groupOrderByFields[dataStoreQueryField.Name] = queryField;
			AddJoins(query, queryField, tableDict);
		}

		ParseAdditionalFeatures(query, dataStoreQuery, countOnly, groupOrderByFields);

		if (!_ignoreAcls && sid.Fields.FirstOrDefault(it => it.Name == StorageSystemField.SysGroups.ToString()) != null)
		{
			AddAclFilter(sid, query);
		}

		if (!withInactive && sid is not RevisionDefinition)
			AddInactiveStandardFilter(sid, filterFieldNames, query);

		if (countOnly)
		{
			if (dataStoreQuery.GroupBy != null && dataStoreQuery.GroupBy.Count > 0)
			{
				query.SelectRaw("1");
				query = new Query().From(query);
			}

			query.SelectCount("* AS CountAll");
		}

		ValidateQuery(query);

		usedDefinitions = tableDict.Values.ToHashSet().ToList();
		usedDefinitions.Add(sid.Name);

		return query;
	}

	private bool FieldsContainAggregation(List<QueryField> selectedFields)
	{
		List<string> aggregationStrings = new() { "sum(", "avg(", "max(", "min(", "count(" };
		foreach (string aggregationString in aggregationStrings)
		{
			IEnumerable<QueryField> res = selectedFields.Where(it => it.Name.ToLower().Contains(aggregationString));
			if (res.Count() > 0)
				return true;
		}

		return false;
	}

	private void AddIsInactiveField(StorageIndexDefinition sid, Query query)
	{
		DataStoreQueryField inactiveDataStoreQueryField = new DataStoreQueryField(StorageSystemField.SysInactiveDate.ToString());
		QueryField inactiveQueryField = new QueryField(_db, inactiveDataStoreQueryField, false, false, null);
		inactiveQueryField.Expression.GetColumnExpressions()[0].TableName = sid.Name;
		query.SelectRaw($"{inactiveQueryField.AsQueryString()} as [{StorageSystemField.SysInactiveDate.ToString()}]");
	}

	private void AddIsFavouriteField(StorageIndexDefinition sid, Query query)
	{
		DataStoreQueryField favouriteDataStoreQueryField = new DataStoreQueryField(StorageSystemField.SysFavourites.ToString());
		QueryField favouriteQueryField = new QueryField(_db, favouriteDataStoreQueryField, false, false, null);
		favouriteQueryField.Expression.GetColumnExpressions()[0].TableName = sid.Name;
		query.SelectRaw($"? = ANY({favouriteQueryField.AsQueryString()}) as [{StorageSystemField.SysIsFavourite.ToString()}]", _db.Authentication!.UserId);
	}

	private void AddInactiveStandardFilter(StorageIndexDefinition sid, List<string> filterFieldNames, Query query)
	{
		if (!filterFieldNames.Contains(StorageSystemField.SysInactiveDate.ToString()))
		{
			var inactiveField = new QueryField(_db, new DataStoreQueryField(StorageSystemField.SysInactiveDate.ToString()), false, true, null);
			inactiveField.Expression.GetColumnExpressions()[0].TableName = sid.Name;
			query.WhereRaw(inactiveField.AsQueryString() + " is NULL");
		}
	}

	internal static void AddJoins(Query query, QueryField queryField, Dictionary<string, string> tableDict)
	{
		foreach (var join in queryField.PreconditionJoins.Values)
		{
			if (tableDict.TryAdd(join.TableAlias, join.Table))
			{
				if (join.InnerJoin)
					query.Join($"{join.Table} AS {join.TableAlias}", join.First, join.Second);
				else
					query.LeftJoin($"{join.Table} AS {join.TableAlias}", join.First, join.Second);
			}
		}
	}

	private void AddAclFilter(StorageIndexDefinition sid, Query query)
	{
		if (_db.Authentication?.Groups == null)
			throw new DataStoreAuthenticationException(
				"Retrieving data without authentication is not permitted. Please open the connection with DataStoreAuthentication.");
		var groupsFieldName = StorageSystemField.SysGroups.ToString();
		var field = sid.Fields.First(f => f.Name == StorageSystemField.SysGroups.ToString());
		var groupsField = new QueryField(_db, new DataStoreQueryField(groupsFieldName), false, true, null)
		{
			DefaultValue = field.DefaultValue,
			MultiValue = field.MultiValue,
			Nullable = field.Nullable,
			Type = field.Type
		};
		groupsField.Expression.GetColumnExpressions()[0].TableName = sid.Name;
		var parsed = (IList)SqlDataHelper.ToType(_db.Authentication.Groups, groupsField)!;
		if (parsed.Count == 0)
			throw new DataStoreAuthenticationException(
				$"User '{_db.Authentication.Username}' has no groups. No documents can be delivered.");

		_db.DataHelper.ArrayOverlap(query, groupsField, parsed);
	}

	private void ValidateQuery(Query query)
	{
		var compiled = _db.ConnectionHelper.GetCompiler().Compile(query);
		var lowerCompiled = compiled.RawSql.ToLower();
		// matches any columns that are not used by their fully qualified name (e.g "TestCol" matches, but "MainTable"."TestCol" does not)
		// tag is special, as it is used in subselects for multivalue equality
		var currLen = 0;
		while (currLen != lowerCompiled.Length)
		{
			currLen = lowerCompiled.Length;
			lowerCompiled = RemoveSubSelect(lowerCompiled);
		}

		var res = Regex.Matches(lowerCompiled.ToLower(), "(?<!join|as|from)[^.]\"\\w+\"[^.]");
		if (res.Count > 0)
		{
			// could be removed once we are in production. but until then, this should be our expectation.
			throw new DataStoreQueryException(
				$"sql query does not meet our requirements. Columns not fully qualified: - {string.Join(", ", res.Select(it => it.Value))}: {lowerCompiled}");
		}
	}

	private void GetFilterFieldNames(List<string> fieldNames, QueryFilterGroup? queryFilterGroup)
	{
		if (queryFilterGroup == null)
			return;

		if (queryFilterGroup.Filters != null)
		{
			foreach (var queryFilter in queryFilterGroup.Filters)
			{
				GetFilterFieldNames(fieldNames, queryFilter);
			}
		}

		if (queryFilterGroup.FilterGroups != null)
		{
			foreach (var filterGroup in queryFilterGroup.FilterGroups)
			{
				GetFilterFieldNames(fieldNames, filterGroup);
			}
		}
	}

	private void GetFilterFieldNames(List<string> fieldNames, QueryFilter filter)
	{
		if (filter.SourceValueType == QueryFilterValueType.Field)
			fieldNames.Add((string)filter.SourceValue!);

		if (filter.CompareValueType == QueryFilterValueType.Field)
		{
			if (filter.CompareValueSource != null)
				fieldNames.Add(filter.CompareValueSource + "." + (string)filter.CompareValue!);
			else
				fieldNames.Add((string)filter.CompareValue!);
		}
	}

	private string RemoveSubSelect(string query)
	{
		var startIndex = query.IndexOf("(select ", StringComparison.Ordinal);
		if (startIndex == -1)
			return query;
		var endIndex = 0;
		var bracketcounter = 0;
		for (int i = startIndex; i < query.Length; i++)
		{
			var c = query[i];
			if (c == '(')
				bracketcounter++;
			else if (c == ')')
				bracketcounter--;

			if (bracketcounter == 0)
			{
				endIndex = i;
				break;
			}
		}

		var q = query.Substring(0, startIndex) + "1" + query.Substring(endIndex + 1);
		return q;
	}

	/// <summary>
	/// Removes characters that would need to be escaped (e.g '.') and append hash chars for uniqueness.
	/// </summary>
	/// <param name="tableName"></param>
	/// <returns>A unique, reproducible string that can be used without escaping characters.</returns>
	internal static string GetTableAlias(string tableName)
	{
		// the hash code should guard against collisions for tables that already contain '_' while this also maintains readability.
		var hashCode = CreateMd5(tableName).Substring(0, 8);
		return ShortenAlias(tableName.Replace(".", "_") + $"_{hashCode}");
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="changedFieldAliases"></param>
	/// <param name="alias"></param>
	/// <returns>A unique, reproducible &amp; as far as possible human-readable string with a max length of MAX_ALIAS_LENGTH characters.</returns>
	[return: NotNullIfNotNull(nameof(alias))]
	public static string? ShortenAlias(string? alias, Dictionary<string, string>? changedFieldAliases = null, int individualLength = MaxDatabaseObjectLength)
	{
		if (alias == null || alias.Length <= individualLength)
			return alias;

		// 8 chars hex code of GetHashCode
		//var hashCode = $"{alias.GetHashCode():X}"; // problem: delivers a different hashcode on every new programm instance
		var hashCode = CreateMd5(alias).Substring(0, 8);

		var allowedLength = individualLength - 8;
		var newAlias = string.Concat(alias.AsSpan(0, allowedLength), hashCode);
		if (changedFieldAliases != null)
			changedFieldAliases[newAlias] = alias;
		return newAlias;
	}

	public static string CreateMd5(string input)
	{
		// Use input string to calculate MD5 hash
		using (System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create())
		{
			byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
			byte[] hashBytes = md5.ComputeHash(inputBytes);

			return Convert.ToHexString(hashBytes);
		}
	}

	/// <summary>
	/// Add additional query parameters (limit, offset, groupBy, orderBy)
	/// </summary>
	/// <param name="query"></param>
	/// <param name="dataStoreQuery"></param>
	/// <param name="countOnly"></param>
	/// <param name="fields"></param>
	private void ParseAdditionalFeatures(Query query, DataStoreQuery dataStoreQuery, bool countOnly,
										 IDictionary<string, QueryField> fields)
	{
		// CountAll
		if (!countOnly)
		{
			// limit
			if (dataStoreQuery.Limit > 0)
				query.Limit(dataStoreQuery.Limit);

			// offset
			if (dataStoreQuery.Offset > 0)
				query.Offset(dataStoreQuery.Offset);
		}

		// Group By
		if (dataStoreQuery.GroupBy != null)
		{
			foreach (var groupBy in dataStoreQuery.GroupBy)
			{
				query.GroupByRaw(fields[groupBy].AsQueryString());
			}
		}

		// Order By
		if (!countOnly && dataStoreQuery.OrderBy != null)
		{
			foreach (var dataStoreElementSort in dataStoreQuery.OrderBy)
			{
				string orderByName = fields[dataStoreElementSort.Name].AsQueryString();

				if (dataStoreElementSort.SortDirection == DataStoreElementSortDirection.Asc)
				{
					query.OrderByRaw(orderByName + " NULLS FIRST");
				}
				else
				{
					query.OrderByRaw(orderByName + " desc NULLS LAST");
				}
			}
		}
	}

	private Query ParseFilterGroup(StorageIndexDefinition sid, Query query, QueryFilterGroup? queryFilterGroup,
								   Dictionary<string, QueryField> fields, Dictionary<string, string> tableDict, List<QueryField> selectedFields)
	{
		if (queryFilterGroup == null)
			return query;

		foreach (var queryFilter in queryFilterGroup.Filters)
		{
			if (queryFilterGroup.LinkType == QueryFilterLinkType.Or)
				query.Or();
			ParseFilter(sid, query, fields.ToDictionary(f => f.Key, f => f.Value.Clone()), queryFilter, tableDict, selectedFields);
		}

		foreach (var filterGroup in queryFilterGroup.FilterGroups)
		{
			if (queryFilterGroup.LinkType == QueryFilterLinkType.Or)
			{
				query.Or();
			}

			query.Where(it => ParseFilterGroup(sid, it, filterGroup, fields, tableDict, selectedFields));
		}

		return query;
	}

	private void ParseFilter(StorageIndexDefinition sid, Query query, Dictionary<string, QueryField> fields, QueryFilter queryFilter,
							 Dictionary<string, string> tableDict, List<QueryField> selectedFields)
	{
		var compOperation = GetCompareOperation(queryFilter.Operator);

		object? compareValue = queryFilter.CompareValue;

		if (queryFilter.CompareValueType == QueryFilterValueType.Subselect
			&& queryFilter.CompareValueSource != null && queryFilter.CompareValue != null)
		{
			if (queryFilter.Operator is QueryFilterOperator.IsNull or QueryFilterOperator.IsNotNull)
				throw new DataStoreQueryException($"Query with (value/field) compared to subselect has {queryFilter.Operator} operator.");

			QueryField subSelect = BuildSubSelect(queryFilter.CompareValueSource, (string)queryFilter.CompareValue, queryFilter.CompareValueConditions,
												  tableDict);
			if (queryFilter.Operator is QueryFilterOperator.Exists or QueryFilterOperator.NotExists)
			{
				if (queryFilter.Operator == QueryFilterOperator.NotExists)
					query.Not();
				query.WhereExists(subSelect.AsQuery());
				return;
			}

			compareValue = subSelect;
		}
		else if (queryFilter.CompareValueType == QueryFilterValueType.Field
				 && queryFilter.CompareValue != null)
		{
			var compareField = fields[(string)queryFilter.CompareValue];
			compareValue = compareField;

			if (compareField is { AggregateFunction: not null })
			{
				if (!compareField.MultiValue)
					throw new DataStoreQueryException(
						"Comparing to field with aggregate function without the field being a multivalue field is not a valid filter.");

				var subSelect = new Query().FromRaw($"unnest({compareField.Expression.GetColumnExpressions()[0].AsString()}) AS aggregated_mvf")
					.SelectAggregate("aggregated_mvf", compareField.AggregateFunction.Value);
				var newQf = new QueryField(_db, subSelect, null, false, compareField, null);
				newQf.MultiValue = false;
				newQf.AggregateFunction = null;
				compareValue = newQf;
			}
		}

		QueryField? tempCompareColumnField = null;
		bool withAggregateFunction = false;
		if (queryFilter.CompareValueType == QueryFilterValueType.Field)
		{
			tempCompareColumnField = fields[(string)queryFilter.CompareValue!];
			withAggregateFunction = tempCompareColumnField.AggregateFunction != null;
		}

		QueryField? tempSourceColumnField = null;
		if (queryFilter.SourceValueType == QueryFilterValueType.Field)
			tempSourceColumnField = fields[(string)queryFilter.SourceValue!];


		if (queryFilter.SourceValueType == QueryFilterValueType.Field && queryFilter.CompareValueType == QueryFilterValueType.Field && !withAggregateFunction)
		{
			FieldToFieldComparison(sid, query, queryFilter, tempSourceColumnField!, tempCompareColumnField!, compOperation, fields);
		}
		else if (queryFilter.SourceValueType == QueryFilterValueType.Value &&
				 queryFilter.CompareValueType is QueryFilterValueType.Field or QueryFilterValueType.Subselect)
		{
			if (compareValue != null && compareValue is QueryField qf && qf.IsQuery)
				ValueToFieldComparison(sid, query, queryFilter, tempCompareColumnField, compOperation, fields, tableDict, qf);
			else
				ValueToFieldComparison(sid, query, queryFilter, tempCompareColumnField, compOperation, fields, tableDict);
		}
		else if (queryFilter.SourceValueType == QueryFilterValueType.Field && !withAggregateFunction && tempSourceColumnField != null &&
				 tempSourceColumnField.MultiValue)
		{
			MvfToValueComparison(sid, query, queryFilter, tempSourceColumnField, compOperation, fields, compareValue!);
		}
		else if (compOperation != null)
		{
			if (DetermineCast(tempSourceColumnField!, queryFilter.Operator, compareValue, out DataStoreFieldType? castFirstTo,
							  out DataStoreFieldType? castSecondTo))
			{
				if (castFirstTo.HasValue)
					tempSourceColumnField.CastTo(castFirstTo.Value);
				if (castSecondTo.HasValue && compareValue is QueryField compareQuery)
					compareQuery.CastTo(castSecondTo.Value);
			}

			if (compareValue is QueryField queryField)
			{
				if (queryField.IsQuery)
					query.WhereRawColumnToSubselect(tempSourceColumnField.AsQueryString(), compOperation, queryField.AsQuery(),
													_db.ConnectionHelper.GetCompiler());
				else
					query.WhereRaw($"{tempSourceColumnField.AsQueryString()} {compOperation} {queryField.AsQueryString()}");
			}
			else
			{
				compareValue = SqlDataHelper.ToType(compareValue, tempSourceColumnField);
				query.WhereRaw($"{tempSourceColumnField.AsQueryString()} {compOperation} ?", compareValue);
			}
		}
		else
		{
			switch (queryFilter.Operator)
			{
				case QueryFilterOperator.NotIn:
				case QueryFilterOperator.NotLike:
				case QueryFilterOperator.IsNotNull:
				case QueryFilterOperator.NotExists:
					query.Not();
					break;
			}

			if (queryFilter.Operator == QueryFilterOperator.FulltextSearch)
			{
				if (compareValue == null)
					return;

				if (!sid.FulltextSearch)
					throw new DataStoreQueryException($"Fulltext search is not enabled for the datasource {sid.Name}.");

				query.Where(q =>
				{
					_db.DataHelper.FulltextQuery(q, sid, (string)compareValue);

					if (_db.FeatureFlags.ExtendedFulltextSearch)
					{
						// split on chars that are not alphanumeric
						var parts = Regex.Split((string)compareValue, "[^a-zA-Z0-9]+");
						var searchStrings = parts.Where(p => !string.IsNullOrWhiteSpace(p)).Select(p => $"%{p.Trim()}%").ToList();

						foreach (var selectedField in selectedFields.Where(
									 f => f is
										  {
											  MultiValue: false,
											  Type: DataStoreFieldType.String or DataStoreFieldType.Text or DataStoreFieldType.Date
													or DataStoreFieldType.DateTime,
											  AggregateFunction: null
										  }
										  && f.Name.Contains('.')))
						{
							q.OrWhere(inner =>
							{
								foreach (var searchStr in searchStrings)
								{
									var compareField = _db.DataHelper.LimitStringLength(selectedField.AsQueryString(), 255);
									// get date part and implicitly cast to text
									if (selectedField.Type is DataStoreFieldType.Date or DataStoreFieldType.DateTime)
										compareField = $"immutable_date_to_string({selectedField.AsQueryString()})";
									inner.WhereRaw($"{compareField} ilike ?", searchStr);
								}

								return inner;
							});
						}
					}

					return q;
				});

				return;
			}

			object? compareTo = null;
			if ((queryFilter.Operator == QueryFilterOperator.In ||
				 queryFilter.Operator == QueryFilterOperator.NotIn) && compareValue is not QueryField)
			{
				compareTo = queryFilter.CompareValue!.ToArray();
			}

			if (queryFilter.SourceValue == null && queryFilter.Operator != QueryFilterOperator.Favourite)
				throw new DataStoreQueryException(
					$"Operation is not {QueryFilterOperator.Exists} or {QueryFilterOperator.NotExists}: ({queryFilter.Operator}) but SourceValue has not been set.");

			if (compareValue is not QueryField)
			{
				if (queryFilter.Operator == QueryFilterOperator.Favourite)
				{
					DataStoreQueryField dataStoreQueryField = new DataStoreQueryField(StorageSystemField.SysFavourites.ToString());
					tempSourceColumnField = new QueryField(_db, dataStoreQueryField, false, false, null);
					tempSourceColumnField.Expression.GetColumnExpressions()[0].TableName = sid.Name;
				}

				var compareToArray = compareValue.IsArray();
				var f = new StorageFieldDefinitionOrm("", tempSourceColumnField!.Type);

				if (queryFilter.Operator is QueryFilterOperator.Like or QueryFilterOperator.NotLike)
				{
					tempSourceColumnField!.Type = DataStoreFieldType.String;
					f = new StorageFieldDefinitionOrm("");
					if (compareToArray)
						throw new DataStoreQueryException(
							$"Cannot do a 'element like array' comparison for '{queryFilter.SourceValue} {queryFilter.Operator} {queryFilter.CompareValue}");
				}

				if (compareToArray)
					f.MultiValue = true;

				if (queryFilter.Operator != QueryFilterOperator.Favourite)
				{
					compareValue = SqlDataHelper.ToType(compareValue, f);
					compareTo = compareValue;
				}
			}

			string column = tempSourceColumnField!.AsQueryString();

			if (DetermineCast(tempSourceColumnField, queryFilter.Operator, compareValue,
							  out DataStoreFieldType? castFirstTo, out DataStoreFieldType? castSecondTo))
			{
				if (castFirstTo.HasValue)
				{
					column = tempSourceColumnField.CastTo(castFirstTo.Value).AsQueryString();
				}

				if (castSecondTo.HasValue && compareValue is QueryField q)
				{
					q.CastTo(castSecondTo.Value);
				}
			}

			switch (queryFilter.Operator)
			{
				case QueryFilterOperator.NotEquals:
					query.Where(it => it.WhereRaw($"{column} != ?", compareValue).OrWhereRaw(column + " is null"));
					break;
				case QueryFilterOperator.In:
					if (compareTo != null)
						query.WhereRaw(column + " in (?)", compareTo);
					else
						query.WhereRaw(column + " in (?)", ((QueryField)compareValue!).AsQuery());
					break;
				case QueryFilterOperator.Favourite:
					if (compareValue != null && compareValue is bool && ((bool)compareValue))
						_db.DataHelper.ArrayContains(query, tempSourceColumnField, _db.Authentication.UserId);
					break;
				case QueryFilterOperator.NotIn:

					query.Not(false).Where(q =>
					{
						q.Not();
						if (compareTo != null)
						{
							q.WhereRaw(column + " not in (?)", compareTo);
						}
						else
						{
							var subselect = ((QueryField)compareValue!).FilterNotNull().AsQuery();
							q.WhereRaw(column + " not in (?)", subselect);
						}

						q.OrWhereRaw(column + " is null");
						return q;
					});
					break;
				case QueryFilterOperator.Like:
				case QueryFilterOperator.NotLike:
					string compareOperator = queryFilter.Operator == QueryFilterOperator.Like ? "ilike" : "not ilike";
					if (tempSourceColumnField.Nullable)
						column = _db.DataHelper.IsNullRaw(column, "''");
					query.WhereRaw($"{column} {compareOperator} ?", compareValue);
					break;
				case QueryFilterOperator.IsNull:
					query.Where(q =>
					{
						q.WhereRaw(column + " is null");
						if (tempSourceColumnField.Type is DataStoreFieldType.String or DataStoreFieldType.Text)
							q.OrWhereRaw(column + " = ?", "");
						return q;
					});
					break;
				case QueryFilterOperator.IsNotNull:
					query.WhereRaw(column + " is not null");
					if (tempSourceColumnField.Type is DataStoreFieldType.String or DataStoreFieldType.Text)
						query.WhereRaw(column + " != ?", "");
					break;
			}
		}
	}

	private bool DetermineCast(QueryField element, QueryFilterOperator compareType,
							   object? compareTo,
							   out DataStoreFieldType? castFirstTo, out DataStoreFieldType? castSecondTo)
	{
		castFirstTo = null;
		castSecondTo = null;
		DataStoreFieldType? first = GetCurrentType(element);
		DataStoreFieldType? second;
		if (compareTo != null)
			second = GetCurrentType(compareTo);
		else
			return false;
		DataStoreFieldType? firstGeneralized = null;
		DataStoreFieldType? secondGeneralized = null;
		if (first.HasValue)
			firstGeneralized = ColumnType.GeneralizeFieldType(first.Value);
		if (second.HasValue)
			secondGeneralized = ColumnType.GeneralizeFieldType(second.Value);

		switch (compareType)
		{
			case QueryFilterOperator.Like or QueryFilterOperator.NotLike:
			{
				if (first != DataStoreFieldType.Text)
					castFirstTo = DataStoreFieldType.String;
				if (second != DataStoreFieldType.Text)
					castSecondTo = DataStoreFieldType.String;
				break;
			}
			case QueryFilterOperator.GreaterThan or QueryFilterOperator.LessThan or QueryFilterOperator.GreaterThanEquals or QueryFilterOperator.LessThanEquals:
			{
				if (firstGeneralized != secondGeneralized)
				{
					if (firstGeneralized is DataStoreFieldType.Time or DataStoreFieldType.DateTime &&
						secondGeneralized is DataStoreFieldType.Time or DataStoreFieldType.DateTime)
					{
						throw new DataStoreQueryException($"Cannot compare Time and Datetime with {compareType}");
					}

					if (firstGeneralized is DataStoreFieldType.Time or DataStoreFieldType.DateTime or DataStoreFieldType.Double &&
						secondGeneralized is DataStoreFieldType.Text)
					{
						castSecondTo = firstGeneralized;
					}
					else if (firstGeneralized is DataStoreFieldType.Text &&
							 secondGeneralized is DataStoreFieldType.Time or DataStoreFieldType.DateTime or DataStoreFieldType.Double)
					{
						castFirstTo = secondGeneralized;
					}
				}

				break;
			}
			case QueryFilterOperator.Equals or QueryFilterOperator.Exists or QueryFilterOperator.NotEquals or QueryFilterOperator.NotExists
				 or QueryFilterOperator.IsNull or QueryFilterOperator.IsNotNull or QueryFilterOperator.In or QueryFilterOperator.NotIn:
			{
				if (firstGeneralized == secondGeneralized)
					break;

				if (firstGeneralized == DataStoreFieldType.Text)
				{
					castSecondTo = DataStoreFieldType.String;
					break;
				}

				if (secondGeneralized == DataStoreFieldType.Text)
				{
					castFirstTo = DataStoreFieldType.String;
					break;
				}

				break;
				//throw new DataStoreQueryException($"Unable to compare {first} to {second} with {compareType}");
			}
		}

		return castFirstTo != null || castSecondTo != null;
	}

	private static DataStoreFieldType GetCurrentType(object compareTo)
	{
		if (compareTo is QueryField qf)
		{
			return qf.Type;
		}

		return DataStoreFieldType.Double;
	}

	private void MvfToValueComparison(StorageIndexDefinition sid, Query query, QueryFilter queryFilter, QueryField field, string? compOperation,
									  Dictionary<string, QueryField> fields, object compareValue)
	{
		if (field.AggregateFunction.HasValue && compOperation != null)
		{
			if (compareValue is QueryField compareQueryField && compareQueryField.IsQuery)
			{
				var compareQuery = compareQueryField.AsQuery();

				if (DetermineCast(field, queryFilter.Operator, compareQueryField,
								  out DataStoreFieldType? castFirstTo, out DataStoreFieldType? castSecondTo))
				{
					if (castFirstTo.HasValue)
						field.CastTo(castFirstTo.Value);
					if (castSecondTo.HasValue)
						compareQueryField.CastTo(castSecondTo.Value);
				}

				var sqlResult = _db.ConnectionHelper.GetCompiler().Compile(compareQuery);
				var sql = Regex.Replace(sqlResult.RawSql, @"([\[\]{}])", @"\$1");
				_db.DataHelper.ArrayAggregate(query, _db.ConnectionHelper.GetCompiler(), field, field.AggregateFunction.Value,
											  compOperation, $"({sql})", sqlResult.Bindings);
			}
			else
			{
				_db.DataHelper.ArrayAggregate(query, _db.ConnectionHelper.GetCompiler(), field, field.AggregateFunction.Value,
											  compOperation, "?",
											  new List<object>() { compareValue });
			}

			return;
		}

		if (queryFilter.Operator is QueryFilterOperator.Like or QueryFilterOperator.NotLike)
		{
			if (queryFilter.Operator is QueryFilterOperator.NotLike)
				query.Not();
			if (compareValue is QueryField subSelect)
			{
				if (queryFilter.Operator is QueryFilterOperator.Like)
					_db.DataHelper.ArrayLike(query, field, subSelect);
				else
					_db.DataHelper.ArrayNotLike(query, field, subSelect);
			}
			else
			{
				if (queryFilter.Operator is QueryFilterOperator.Like)
					_db.DataHelper.ArrayLike(query, field, (string)compareValue);
				else
					_db.DataHelper.ArrayNotLike(query, field, (string)compareValue);
			}

			return;
		}

		if (queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals && compareValue.IsArray())
		{
			IEnumerable<object> compareTo = queryFilter.CompareValue!.ToArray();

			var parsed = (IList)SqlDataHelper.ToType(compareTo, field)!;
			if (queryFilter.Operator == QueryFilterOperator.Equals)
				_db.DataHelper.ArrayEqual(query, field, parsed);
			else
				_db.DataHelper.ArrayNotEqual(query, field, parsed);

			return;
		}

		throw new DataStoreQueryException($"Unable to give the query {queryFilter.SourceValue} {queryFilter.Operator} '{compareValue}' any meaning.");
	}

	public static Query GetMultiValueFieldJoin(StorageIndexDefinition sid, long multiValueFieldId)
	{
		return new Query(sid.Name).GetMultiValueFieldJoin(sid, multiValueFieldId);
	}

	private void ValueToFieldComparison(StorageIndexDefinition sid, Query query, QueryFilter queryFilter, QueryField? compareField,
										string? compOperation, Dictionary<string, QueryField> fields, Dictionary<string, string> tableDict,
										QueryField? compareToSubselectField = null)
	{
		if (queryFilter.SourceValue == null)
			throw new DataStoreQueryException("SourceCompareType = Value, but value is null");

		if (compareToSubselectField != null)
		{
			var compareToSubselect = compareToSubselectField.AsQuery();
			object firstCompare = queryFilter.SourceValue;
			if (queryFilter.Operator == QueryFilterOperator.NotEquals)
				compOperation = "!=";
			if (compOperation != null && !queryFilter.SourceValue.IsArray())
			{
				var column = compareToSubselect.GetOneComponent("select");
				if (column is AggregatedColumn && compareField != null)
				{
					var doubleType = new QueryField(_db, new DataStoreQueryField(""), false, true, null);
					doubleType.Type = DataStoreFieldType.Double;
					firstCompare = SqlDataHelper.ToType(firstCompare, doubleType)!;
					var select = (AggregatedColumn)column;
					compareToSubselect.ClearComponent("select");
					if (_db.InlineMultiValueFields)
						compareToSubselect.GroupBy($"{sid.Name}.{StorageSystemField.Id.ToString()}").Select($"{sid.Name}.{StorageSystemField.Id.ToString()}").HavingRaw(
							$"? {compOperation} {_db.ConnectionHelper.GetCompiler().CompileColumn(new SqlResult(), select)}", firstCompare);
					else
						compareToSubselect.GroupBy($"{sid.Name}.{StorageSystemField.Id.ToString()}").Select($"{sid.Name}.{StorageSystemField.Id.ToString()}").HavingRaw(
							$"? {compOperation} {_db.ConnectionHelper.GetCompiler().CompileColumn(new SqlResult(), select)}", firstCompare);
					query.WhereIn($"{sid.Name}.{StorageSystemField.Id.ToString()}", compareToSubselect);
				}
				else
				{
					query.WhereRaw(_db.ConnectionHelper.GetCompiler(), firstCompare, compOperation, compareToSubselect);
				}
			}
			else if (queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals && queryFilter.SourceValue.IsArray())
			{
				var arr = queryFilter.SourceValue.ToArray();
				string separator = ",-, ";
				var compareTo = string.Join(separator, arr.Order());

				var column = compareToSubselect.GetOneComponent("select");
				string columnStr = "";
				if (column is RawColumn rawSelect)
					columnStr = rawSelect.Expression;
				else if (column is Column select)
					columnStr = select.Name;
				else
				{
					throw new NotImplementedException($"Could not parse column of type {column.GetType()}");
				}

				compareToSubselect.HavingRaw($"{_db.DataHelper.StringAggregateRaw(columnStr, separator, columnStr)} {compOperation} ?", compareTo);
				compareToSubselect.ClearComponent("select");
				compareToSubselect.Select("1");

				query.WhereExists(compareToSubselect);
			}
			else if (queryFilter.Operator is QueryFilterOperator.In or QueryFilterOperator.NotIn)
			{
				firstCompare = SqlDataHelper.ToType(queryFilter.SourceValue, compareToSubselectField)!;
				if (queryFilter.Operator == QueryFilterOperator.In)
					query.WhereIn(firstCompare, compareToSubselect, _db.ConnectionHelper.GetCompiler());
				else
					query.WhereNotIn(firstCompare, compareToSubselect, _db.ConnectionHelper.GetCompiler());
			}
			else if (queryFilter.Operator is QueryFilterOperator.Like or QueryFilterOperator.NotLike)
			{
				if (!queryFilter.SourceValue.IsArray())
					throw new DataStoreQueryException(
						$"Operator {queryFilter.Operator} does only work if SourceValue is an array. SourceValue: {queryFilter.SourceValue}");
				IEnumerable<object> arr = queryFilter.SourceValue.ToArray();
				var enumerable = arr as object[] ?? arr.ToArray();
				if (enumerable.Length == 0)
					throw new DataStoreQueryException($"Cannot compare empty array {queryFilter.Operator} subselect");

				var array = new Query().SelectRaw("'1'").From(q =>
					{
						q.SelectRaw("'" + enumerable[0] + "' AS val");
						for (int i = 1; i < enumerable.Length; i++)
						{
							q.UnionAll(new Query().SelectRaw("'" + enumerable[i] + "'"));
						}

						q.As("arr");
						return q;
					})
					.WhereLike("[arr].[val]", compareToSubselect, _db.ConnectionHelper.GetCompiler());

				if (queryFilter.Operator == QueryFilterOperator.NotLike)
					query.Not();

				query.WhereExists(array);
			}
			else
			{
				throw new DataStoreQueryException($"Unable to determine query for filter {queryFilter}");
			}

			return;
		}


		if (!compareField!.MultiValue && queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals or QueryFilterOperator.GreaterThan
																 or QueryFilterOperator.GreaterThanEquals or QueryFilterOperator.LessThan
																 or QueryFilterOperator.LessThanEquals)
		{
			QueryFilterField queryFilterCompareField = new((string)queryFilter.CompareValue!);
			QueryFilter newFilter = queryFilter.Operator switch
			{
				QueryFilterOperator.Equals => new EqualsFilter(queryFilterCompareField, queryFilter.SourceValue!),
				QueryFilterOperator.NotEquals => new NotEqualsFilter(queryFilterCompareField, queryFilter.SourceValue!),
				QueryFilterOperator.GreaterThan => new LessThanFilter(queryFilterCompareField, queryFilter.SourceValue!),
				QueryFilterOperator.GreaterThanEquals => new LessThanEqualsFilter(queryFilterCompareField, queryFilter.SourceValue!),
				QueryFilterOperator.LessThan => new GreaterThanFilter(queryFilterCompareField, queryFilter.SourceValue!),
				QueryFilterOperator.LessThanEquals => new GreaterThanEqualsFilter(queryFilterCompareField, queryFilter.SourceValue!),
				_ => throw new DataStoreQueryException($"Error: comparison for compare type value {queryFilter.Operator} column has not been implemented")
			};
			ParseFilter(sid, query, fields, newFilter, tableDict, []);
			return;
		}

		var generalizedFieldType = ColumnType.GeneralizeFieldType(compareField.Type);
		if (queryFilter.SourceValue is int or long or double && generalizedFieldType != DataStoreFieldType.Double
			|| queryFilter.SourceValue is DateTime && generalizedFieldType != DataStoreFieldType.DateTime && generalizedFieldType != DataStoreFieldType.Time
			|| queryFilter.SourceValue is string && generalizedFieldType != DataStoreFieldType.Text
			|| queryFilter.SourceValue is bool && generalizedFieldType != DataStoreFieldType.Boolean)
			throw new DataStoreQueryException(
				$"SourceValueType {queryFilter.SourceValue.GetType()} and CompareValueType {compareField.Type} can not be compared.");

		if (queryFilter.Operator is QueryFilterOperator.NotIn or QueryFilterOperator.NotEquals or QueryFilterOperator.NotLike)
			query.Not();

		var compareFieldNameRaw = compareField.AsQueryString();

		if (compareField.MultiValue)
		{
			var possibleCompareOperators = new[]
			{
				QueryFilterOperator.In, QueryFilterOperator.NotIn, QueryFilterOperator.Equals, QueryFilterOperator.NotEquals, QueryFilterOperator.Like,
				QueryFilterOperator.NotLike
			};
			if (!possibleCompareOperators.Contains(queryFilter.Operator))
				throw new DataStoreQueryException(
					$"Cannot compare value to multivalue field with operator {queryFilter.Operator}. Supported operators are: {string.Join(", ", possibleCompareOperators)}");

			if (queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals)
			{
				if (!queryFilter.SourceValue.IsArray())
					throw new DataStoreQueryException($"Can only compare array to multivalue field using {queryFilter.Operator}");
			}
			else if (queryFilter.SourceValue.IsArray())
				throw new DataStoreQueryException($"Cant compare array to multivalue field using {queryFilter.Operator}");

			if (queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals)
			{
				QueryFilter qf;
				if (queryFilter.Operator == QueryFilterOperator.Equals)
					qf = new EqualsFilter(new QueryFilterField((string)queryFilter.CompareValue!), queryFilter.SourceValue);
				else
					qf = new NotEqualsFilter(new QueryFilterField((string)queryFilter.CompareValue!), queryFilter.SourceValue);
				var columnName = fields[(string)queryFilter.CompareValue!];
				MvfToValueComparison(sid, query, qf, columnName, compOperation, fields, queryFilter.SourceValue);
			}
			else if (queryFilter.Operator is QueryFilterOperator.In or QueryFilterOperator.NotIn)
			{
				if (queryFilter.Operator is QueryFilterOperator.In)
					_db.DataHelper.ArrayContains(query, compareField, queryFilter.SourceValue);
				else
					_db.DataHelper.ArrayNotContains(query, compareField, queryFilter.SourceValue);
			}
			// Like/ NotLike
			else
			{
				if (queryFilter.Operator == QueryFilterOperator.NotLike)
				{
					_db.DataHelper.ArrayNotLike(query, (string)queryFilter.SourceValue, compareField);
				}
				else
				{
					_db.DataHelper.ArrayLike(query, (string)queryFilter.SourceValue, compareField);
				}
			}
		}
		else
		{
			var possibleCompareOperators = new[] { QueryFilterOperator.Like, QueryFilterOperator.NotLike };
			if (!possibleCompareOperators.Contains(queryFilter.Operator))
				throw new DataStoreQueryException(
					$"Cannot compare value to field with operator {queryFilter.Operator}. Supported operators are: {string.Join(", ", possibleCompareOperators)}");

			if (queryFilter.Operator == QueryFilterOperator.NotLike)
				query.WhereRaw($"? not like {_db.DataHelper.IsNullRaw(compareFieldNameRaw, "''")}", queryFilter.SourceValue);
			else
				query.WhereRaw($"? like {compareFieldNameRaw}", queryFilter.SourceValue);
		}
	}

	private void FieldToFieldComparison(StorageIndexDefinition sid, Query query, QueryFilter queryFilter, QueryField sourceField,
										QueryField compareField,
										string? compOperation, IDictionary<string, QueryField> fields)
	{
		if (sourceField.Type != compareField.Type
			&& ColumnType.GeneralizeFieldType(sourceField.Type) != ColumnType.GeneralizeFieldType(compareField.Type))
			throw new DataStoreQueryException(
				$"SourceFieldType '{sourceField.Name}::{sourceField.Type}' and CompareFieldType '{compareField.Name}::{compareField.Type}' can not be compared");
		List<QueryFilterOperator> allowedCompareTypes;
		if (compareField.MultiValue)
		{
			allowedCompareTypes = new[] { QueryFilterOperator.In, QueryFilterOperator.NotIn, QueryFilterOperator.Like, QueryFilterOperator.NotLike }.ToList();
			if (sourceField.MultiValue)
			{
				allowedCompareTypes.Add(QueryFilterOperator.Equals);
				allowedCompareTypes.Add(QueryFilterOperator.NotEquals);
			}
		}
		else
		{
			allowedCompareTypes = new[] { QueryFilterOperator.Equals, QueryFilterOperator.NotEquals }.Concat(
				ColumnType.GeneralizeFieldType(sourceField.Type) switch
				{
					DataStoreFieldType.DateTime or DataStoreFieldType.Time or DataStoreFieldType.Double => new[]
					{
						QueryFilterOperator.GreaterThan, QueryFilterOperator.GreaterThanEquals, QueryFilterOperator.LessThan, QueryFilterOperator.LessThanEquals
					},
					DataStoreFieldType.Text => new[] { QueryFilterOperator.Like, QueryFilterOperator.NotLike },
					_ => Array.Empty<QueryFilterOperator>()
				}).ToList();
		}

		if (!allowedCompareTypes.Contains(queryFilter.Operator))
			throw new DataStoreQueryException(
				$"Unable to compare field {sourceField.Name}:{sourceField.Type} with {compareField.Name}{compareField.Type} (compareGroupType: {ColumnType.GeneralizeFieldType(compareField.Type)}) with compare operation {queryFilter.Operator}. Allowed compare operations are: {string.Join(", ", allowedCompareTypes)}");
		if (!compareField.MultiValue)
		{
			if (queryFilter.Operator == QueryFilterOperator.NotEquals)
			{
				compOperation = "!=";
				sourceField.Name = _db.DataHelper.IsNullRaw(sourceField.Name, "''");
				compareField.Name = _db.DataHelper.IsNullRaw(compareField.Name, "''");
			}

			query.WhereRaw($"{sourceField.AsQueryString()} {compOperation} {compareField.AsQueryString()}");
		}
		else
		{
			if (queryFilter.Operator is QueryFilterOperator.Equals or QueryFilterOperator.NotEquals)
			{
				if (queryFilter.Operator == QueryFilterOperator.Equals)
					_db.DataHelper.ArrayEqualColumns(query, sourceField.AsQueryString(), compareField.AsQueryString());
				else
					_db.DataHelper.ArrayNotEqualColumns(query, sourceField.AsQueryString(), compareField.AsQueryString());
			}
			else
			{
				if (queryFilter.Operator is QueryFilterOperator.NotIn or QueryFilterOperator.NotLike)
					query.Not();

				string compareOperator = "=";
				if (queryFilter.Operator is QueryFilterOperator.Like or QueryFilterOperator.NotLike)
					compareOperator = "like";
				_db.DataHelper.ColumnToArrayColumn(query, sourceField.AsQueryString(), queryFilter.Operator, compareOperator, compareField.AsQueryString());
			}
		}
	}

	private QueryField BuildSubSelect(string dataSourceName, string queryFilterFieldName, QueryFilterGroup? filters, Dictionary<string, string> tableDict)
	{
		StorageIndexDefinition? sid = _getStorageIndexDefinition(dataSourceName ?? "");
		if (sid == null)
			throw new DataStoreQueryException(
				$"Error while building subselect for {nameof(QueryFilterField)}: DataSource '{dataSourceName}' does not exist.");

		Query query = new(dataSourceName);
		var fieldParser = new FieldParser(_db, _getStorageIndexDefinition, _lang, _defaultLang, sid, new());
		var selectField = fieldParser.Parse(new DataStoreQueryField(queryFilterFieldName));
		query.Select(selectField);
		var returnField = new QueryField(_db, query, null, false, selectField, null);

		var fieldNames = new List<string>();
		GetFilterFieldNames(fieldNames, filters);
		var fields = new Dictionary<string, QueryField>();
		var theseTables = new Dictionary<string, string>();
		foreach (var dataStoreQueryField in fieldNames)
		{
			var queryField = fieldParser.Parse(new DataStoreQueryField(dataStoreQueryField), allowAggregateFunctions: false);
			fields[dataStoreQueryField] = queryField;
			AddJoins(query, queryField, theseTables);
		}

		ParseFilterGroup(sid, query, filters, fields, theseTables, []);

		foreach (var entry in theseTables)
		{
			tableDict[Guid.NewGuid().ToString()] = entry.Value;
		}

		return returnField;
	}

	private string? GetCompareOperation(QueryFilterOperator queryFilterCompareType)
	{
		return queryFilterCompareType switch
		{
			QueryFilterOperator.Equals => "=",
			// NotEquals requires special nullablity handling
			//QueryFilterOperator.NotEquals => "!=",
			QueryFilterOperator.GreaterThan => ">",
			QueryFilterOperator.LessThan => "<",
			QueryFilterOperator.GreaterThanEquals => ">=",
			QueryFilterOperator.LessThanEquals => "<=",
			_ => null
		};
	}
}