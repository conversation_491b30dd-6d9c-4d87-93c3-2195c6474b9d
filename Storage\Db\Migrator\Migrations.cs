using System.Data;
using System.Text.RegularExpressions;
using FluentMigrator;
using FluentMigrator.Postgres;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Domain.Storage.Db.Postgres;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Serilog;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Db.Migrator;

public class Migrations
{
	private StorageConnection _connection;
	private SqlMigrator _sqlMigrator;
	private readonly SqlDataHelper _sqlDataHelper;
	private readonly SqlConnectionHelper _sqlConnectionHelper;

	/// <summary>
	/// A list containing all the migrations that should have been executed.
	/// </summary>
	public IDictionary<StorageMigration, Action<Migration>> MigrationList { get; }

	public Migrations(StorageConnection connection, ILogger logger)
	{
		_connection = connection;
		_sqlMigrator = connection.Db.Migrator;
		_sqlDataHelper = connection.Db.DataHelper;
		_sqlConnectionHelper = connection.Db.ConnectionHelper;

		MigrationList = new Dictionary<StorageMigration, Action<Migration>>
		{
			// add migrations
			[new StorageMigration("Add_New_Storage_FileId_Field", "This migration adds the new Field StorageFileId to all data tables.")] =
				MigrateNewStorageFileIdField,
			[new StorageMigration("Add_New_Storage_RevisionHash_Field", "This migration adds the new Field StorageRevisionHash to all data tables.")] =
				MigrateNewStorageRevisionHashField,
			[new StorageMigration("Exclude_SysDeleted_From_UniqueConstrains",
								  $"Removes the old unique constraint and adds a unique index excluding {StorageSystemField.SysIsDeleted} entries.")] =
				FixExcludeSysDeletedFromUniqueConstrains,
			[new StorageMigration("Add_Fk_To_Storage_PathId", "This migration adds the foreign key (if missing) of " +
															  "StoragePathId to StoragePath.Id in every data table .")] =
				AddFkToStoragePathId,
			[new StorageMigration("Primitive_ColumnTypes_To_NotNullable_And_Add_DefaultValue",
								  $"Changes nullable primitive column types to not nullable and adds default values. DateTime columns will only get default now().")] =
				ChangePrimitiveColumnTypesToNotNullableAndAddDefaultValue,
			[new StorageMigration("Add_New_Storage_SysInactiveDate_Field",
								  $"Adds new field 'SysInactiveDate' to Storage, to save datetime of inactivation of data set.")] =
				MigrateNewStorageSysInactiveDateField,
			[new StorageMigration("Add_New_Storage_SysFavourite_Field",
								  $"Adds new field 'SysFavourite' to Storage, to mark a certain data set as favourite for a user.")] =
				MigrateNewStorageSysFavouriteField,
			[new StorageMigration("Change_New_System_Fields_Readonly_And_SystemField_Flag",
								  $"Sets the possibly missing readonly and system field flags to the existing fields.")] =
				ChangeReadonlyAndSystemFieldFlag,
			[new StorageMigration("Fix_Long_Index_And_Constraint_Names",
								  $"Drops all existing indexes and constraints and recreates them.")] =
				FixLongIndexAndConstraintNames,
			[new StorageMigration("Add_New_Storage_FileId_Field_Part_2", "This migration adds the new Field StorageFileId to all data tables. - a second time completed")] =
				MigrateNewStorageFileIdField,
			[new StorageMigration("Add_New_Storage_RevisionHash_Field_Part_2", "This migration adds the new Field StorageRevisionHash to all data tables. - a second time completed")] =
				MigrateNewStorageRevisionHashField,
			[new StorageMigration("Add_New_Storage_SysInactiveDate_Field_Part_2",
								  $"Adds new field 'SysInactiveDate' to Storage, to save datetime of inactivation of data set. - a second time completed")] =
				MigrateNewStorageSysInactiveDateField,
			[new StorageMigration("Add_New_Storage_SysFavourite_Field_Part_2",
								  $"Adds new field 'SysFavourite' to Storage, to mark a certain data set as favourite for a user. - a second time completed")] =
				MigrateNewStorageSysFavouriteField,
			[new StorageMigration("Primitive_ColumnTypes_To_NotNullable_And_Add_DefaultValue_Again",
								  $"Updates on fields were not using the default value in special cases.")] =
				ChangePrimitiveColumnTypesToNotNullableAndAddDefaultValue,
			[new StorageMigration("Id_Columns_To_UUID_Column_Type",
								  $"Changes the data type of id columns to uuid")] =
				ChangeIdColumnsToUuidColumnType,
			[new StorageMigration("Add_Date_Time_Columns_To_Fulltext_Index",
								  $"Adds Date and DateTime columns to the fulltext index")] =
				AddDateTimeToFulltextIndex,
			// ...
		};
	}

	private void ChangeReadonlyAndSystemFieldFlag(Migration obj)
	{
		_connection.WithDbContext(db =>
		{
			List<string> fields = new List<string>()
			{
				StorageSystemField.SysFileId.ToString(),
				StorageSystemField.SysRevisionHash.ToString(),
				StorageSystemField.SysInactiveDate.ToString(),
				StorageSystemField.SysFavourites.ToString()
			};
			List<StorageIndexDefinition> definitionList = db.StorageIndexDefinition.Include(it => it.Fields).ToList();
			foreach (var storageIndexDefinition in definitionList)
			{
				foreach (string fieldName in fields)
				{
					try
					{
						storageIndexDefinition.Fields.Where(it => it.Name == fieldName).First().Readonly = true;
					}
					catch (Exception)
					{
						// ignored
					}

					try
					{
						storageIndexDefinition.Fields.Where(it => it.Name == fieldName).First().SystemField = true;
					}
					catch (Exception)
					{
						// ignored
					}

					db.SaveChanges();
				}
			}

			return 0;
		});
	}

	private void AddFkToStoragePathId(Migration migration)
	{
		List<StorageIndexDefinition> definitionList =
			_connection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });
		foreach (var storageIndexDefinition in definitionList)
		{
			try
			{
				StorageFieldDefinitionOrm? pathIdField = storageIndexDefinition.Fields
					.FirstOrDefault(field => field.Name == StorageSystemField.SysPathId.ToString());

				if (pathIdField == null)
					continue;

				// id of path entry in storage path table for file data
				StorageForeignKey storagePathFk = new StorageForeignKey(nameof(StoragePath), StoragePath.IdName, Rule.Cascade);
				var sysFilePathId = new StorageFieldDefinitionOrm(StorageSystemField.SysPathId.ToString())
				{
					Type = DataStoreFieldType.Long,
					Nullable = true,
					ForeignKey = storagePathFk
				};
				Dictionary<string, StorageFieldDefinitionOrm> fields = new();
				fields.Add(StorageSystemField.SysPathId.ToString(), sysFilePathId);
				_sqlMigrator.Update(storageIndexDefinition, fields, null);
			}
			catch (Exception e)
			{
				if (!e.Message.ToLower().Contains("already exists"))
					throw new DataStoreOperationException($"Unable to {nameof(AddFkToStoragePathId)} for {storageIndexDefinition.Name}", e);
			}
		}
	}

	private void MigrateNewStorageRevisionHashField(Migration migration)
	{
		var sysRevisionHash = new StorageFieldDefinitionOrm(StorageSystemField.SysRevisionHash.ToString())
		{
			Type = DataStoreFieldType.String,
			Nullable = false,
			DefaultValue = "",
			Length = 32
		};

		AddSystemFieldIfNotExists(nameof(MigrateNewStorageRevisionHashField), sysRevisionHash);
	}

	private void MigrateNewStorageFileIdField(Migration migration)
	{
		var sysFileRevisionId = new StorageFieldDefinitionOrm(StorageSystemField.SysFileId.ToString())
		{
			Type = DataStoreFieldType.String,
			Nullable = true,
			Length = 50
		};

		AddSystemFieldIfNotExists(nameof(MigrateNewStorageSysFavouriteField), sysFileRevisionId);
	}

	private void MigrateNewStorageSysInactiveDateField(Migration migration)
	{
		var sysInactiveDate = new StorageFieldDefinitionOrm(StorageSystemField.SysInactiveDate.ToString())
		{
			Type = DataStoreFieldType.DateTime,
			Nullable = true
		};

		AddSystemFieldIfNotExists(nameof(MigrateNewStorageSysFavouriteField), sysInactiveDate);
	}

	private void MigrateNewStorageSysFavouriteField(Migration migration)
	{
		var sysFavourites = new StorageFieldDefinitionOrm(StorageSystemField.SysFavourites.ToString())
		{
			Type = DataStoreFieldType.Guid,
			MultiValue = true,
			Nullable = true
		};

		AddSystemFieldIfNotExists(nameof(MigrateNewStorageSysFavouriteField), sysFavourites);
	}

	private void AddSystemFieldIfNotExists(string migrationName, StorageFieldDefinitionOrm newField)
	{
		newField.Readonly = true;
		newField.SystemField = true;

		List<StorageIndexDefinition> definitionList =
			_connection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });
		List<StorageIndexDefinition> cleanedDefinitionList = new List<StorageIndexDefinition>();
		foreach (var storageIndexDefinition in definitionList)
		{
			if (storageIndexDefinition.Fields.Where(field => field.Name == newField.Name).FirstOrDefault() == null)
				cleanedDefinitionList.Add(storageIndexDefinition);
		}

		foreach (var storageIndexDefinition in cleanedDefinitionList)
		{
			StorageFieldDefinitionOrm fieldToSave = new StorageFieldDefinitionOrm(newField.ToDto());
			try
			{
				_connection.WithDbContext(db =>
				{
					var definition = db.StorageIndexDefinition
						.Include(it => it.Fields)
						.FirstOrDefault(it => it.Name == storageIndexDefinition.Name);

					definition!.Fields.Add(fieldToSave);
					db.SaveChanges();
					return 0;
				});
				_sqlMigrator.Create(storageIndexDefinition, new List<StorageFieldDefinitionOrm> { fieldToSave }, null);
			}
			catch (Exception e)
			{
				if (!e.Message.ToLower().Contains("already exists"))
					throw new DataStoreOperationException($"Unable to {migrationName} for {storageIndexDefinition.Name}", e);
			}
		}
	}

	private void FixExcludeSysDeletedFromUniqueConstrains(Migration migration)
	{
		List<StorageIndexDefinition> definitionList =
			_connection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });
		foreach (var storageIndexDefinition in definitionList)
		{
			foreach (var uniqueField in storageIndexDefinition.Fields.Where(it => it.Unique && !it.PrimaryKey))
			{
				// delete old constraint
				var constraintName = SqlMigrator.GetConstraintName(false, storageIndexDefinition.Name, uniqueField.Name);
				if (migration.Schema.Table(storageIndexDefinition.Name).Constraint(constraintName).Exists())
				{
					migration.Delete.UniqueConstraint().FromTable(storageIndexDefinition.Name).Column(uniqueField.Name);
				}

				// add new index with filter
				var indexName = SqlMigrator.GetIndexName(storageIndexDefinition.Name, uniqueField.Name);
				if (!migration.Schema.Table(storageIndexDefinition.Name).Index(indexName).Exists())
				{
					migration.Create.Index()
						.OnTable(storageIndexDefinition.Name)
						.OnColumn(uniqueField.Name)
						.Unique()
						.WithOptions()
						.Filter($"\"{StorageSystemField.SysIsDeleted}\" is false");
				}
			}
		}
	}

	private void ChangePrimitiveColumnTypesToNotNullableAndAddDefaultValue(Migration migration)
	{
		List<StorageIndexDefinition> definitionList =
			_connection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });
		foreach (var storageIndexDefinition in definitionList)
		{
			foreach (var field in storageIndexDefinition.Fields
						 .Where(field => field is { PrimaryKey: false, MultiValue: false, SystemField: false, Translatable: false, LookupSource: null }
										 && !field.Name.Contains("__")))
			{
				// set default value first
				object? defaultValue = field.Type switch
				{
					DataStoreFieldType.Boolean => false,
					DataStoreFieldType.Integer or DataStoreFieldType.Long or DataStoreFieldType.Double => 0,
					DataStoreFieldType.String or DataStoreFieldType.Text => "",
					DataStoreFieldType.Date or DataStoreFieldType.DateTime or DataStoreFieldType.Time =>
						((field.Nullable) ? null : SystemMethods.CurrentDateTime.ToString()),
					_ => null
				};

				if (defaultValue != null)
				{
					bool migrationRan = false;
					if (field.Type is DataStoreFieldType.Date or DataStoreFieldType.DateTime or DataStoreFieldType.Time)
					{
						if (field.DefaultValue != defaultValue)
						{
							field.DefaultValue = defaultValue;
							var mig = migration.Alter.Column(field.Name)
								.OnTable(storageIndexDefinition.Name)
								.AsDbType(_sqlDataHelper, field.Type, field.Length)
								.WithDefault(SystemMethods.CurrentDateTime);

							if (field.Nullable)
								mig.Nullable();
							else
								mig.NotNullable();

							migrationRan = true;
						}
					}
					else
					{
						if (field.DefaultValue != defaultValue || field.Nullable)
						{
							field.DefaultValue = defaultValue;

							_sqlConnectionHelper.WithQueryFactory(qf =>
							{
								qf.Query(storageIndexDefinition.Name).WhereNull(field.Name).Update(new Dictionary<string, object?>()
								{
									{ field.Name, field.DefaultValue }
								});
							});

							migration.Alter.Column(field.Name)
								.OnTable(storageIndexDefinition.Name)
								.AsDbType(_sqlDataHelper, field.Type, field.Length)
								.WithDefaultValue(field.DefaultValue)
								.NotNullable();

							field.Nullable = false;
							migrationRan = true;
						}
					}

					if (migrationRan)
					{
						_connection.WithDbContext(db =>
						{
							var storageField = db.StorageFieldDefinition.First(
								f => f.StorageIndexDefinitionId == field.StorageIndexDefinitionId && f.Name == field.Name);
							storageField.Nullable = field.Nullable;
							storageField.DefaultValue = field.DefaultValue;
							db.SaveChanges();
							return 0;
						});
					}
				}
			}
		}
	}

	private class DbObject
	{
		public string Tablename { get; set; } = null!;
		public string Name { get; set; } = null!;
		public string Statement { get; set; } = null!;
	}

	/// <summary>
	/// All database objects are silently shortened by postgres to a max length of 63 chars.
	/// We did have custom algorithms in place to avoid collisions, but they were not permanent (application restarts)
	/// This method fetches all indexes and constraints and tries to validate and rename them if necessary.
	/// This method also deletes some duplicate indexes that were present, because we marked primary keys as unique as well, which resulted in the same index being created twice.
	/// </summary>
	/// <param name="migration"></param>
	/// <exception cref="DataStoreOperationException"></exception>
	private void FixLongIndexAndConstraintNames(Migration migration)
	{
		List<StorageIndexDefinition> definitionList =
			_connection.WithDbContext(db => { return db.StorageIndexDefinition.Include(it => it.Fields).ToList(); });
		var efCoreTableNames = _connection.WithDbContext(db => db.Model.GetEntityTypes().Select(type => type.GetTableName()).Distinct().ToList());

		var indexes = _connection.WithDbContext(db => db.Database.SqlQuery<DbObject>($"""
																					  SELECT
																					      tablename as Tablename,
																					      indexname as Name,
																					      indexdef as Statement
																					  FROM
																					      pg_indexes
																					  where length(indexname) = {FilterParser.MaxDatabaseObjectLength}
																					  order by tablename
																					  """).ToList());

		var constraints = _connection.WithDbContext(db => db.Database.SqlQuery<DbObject>($"""
																						  SELECT rel.relname as Tablename
																						        ,conname as Name
																						        ,pg_get_constraintdef(c.oid) as Statement
																						  FROM   pg_constraint c
																						  JOIN   pg_namespace n ON n.oid = c.connamespace
																						  JOIN pg_catalog.pg_class rel ON rel.oid = c.conrelid
																						  WHERE  contype IN ('f', 'p ')
																						  and length(conname) = {FilterParser.MaxDatabaseObjectLength}
																						  order by rel.relname
																						  """).ToList());

		foreach (var index in indexes)
		{
			if (efCoreTableNames.Contains(index.Tablename))
				continue;

			var definition = GetDefinitionAndField(definitionList, index, out var field);

			if (field == null)
				continue;

			if (index.Name.StartsWith("PK_"))
				continue;

			// we did create the unique index twice for Primary keys.
			if (index.Statement.StartsWith("CREATE UNIQUE INDEX \"IX_") && index.Statement.EndsWith("\" USING btree (\"Id\")"))
			{
				migration.Execute.Sql($"""
									   DROP INDEX "{index.Name}"
									   """);
				continue;
			}

			var newIndexName = SqlMigrator.GetIndexName(definition.Name, field.Name);
			if (newIndexName != index.Name)
				migration.Execute.Sql($"""
									   ALTER INDEX "{index.Name}" RENAME TO "{newIndexName}"
									   """);
		}

		List<string> fieldsToIgnore = [StorageSystemField.SysPathId.ToString(), RevisionDefinition.ElementId];
		foreach (var constraint in constraints)
		{
			if (efCoreTableNames.Contains(constraint.Tablename))
				continue;

			var definition = GetDefinitionAndField(definitionList, constraint, out var field);

			if (field == null || fieldsToIgnore.Contains(field.Name))
				continue;

			string newConstraintName;
			if (constraint.Name.StartsWith("UC_") || constraint.Name.StartsWith("PK_"))
			{
				bool isPrimaryKey = constraint.Name.StartsWith("PK_");
				newConstraintName = SqlMigrator.GetConstraintName(isPrimaryKey, definition.Name, field.Name);
			}
			else // FK_
			{
				if (field.LookupSource == null)
					throw new DataStoreOperationException(
						$"Field {field.Name} of definition {definition.Name} does not have a LookupSource, but a foreign key constraint. {constraint.Name}: {constraint.Statement}");
				newConstraintName = SqlMigrator.GetForeignKeyConstraintName(definition.Name, field.Name, field.LookupSource);
			}

			if (newConstraintName != constraint.Name)
				migration.Execute.Sql($"""
									   ALTER TABLE "{definition.Name}" RENAME CONSTRAINT "{constraint.Name}" TO "{newConstraintName}"
									   """);
		}
	}

	private static StorageIndexDefinition GetDefinitionAndField(List<StorageIndexDefinition> definitionList, DbObject dbObject,
																out StorageFieldDefinitionOrm? field)
	{
		var definition = definitionList.FirstOrDefault(d => d.Name == dbObject.Tablename);
		if (definition == null)
		{
			var lastPart = dbObject.Tablename.Split("_").Last();
			if ("Revision".StartsWith(lastPart))
			{
				definition = definitionList.FirstOrDefault(d => d.Name == dbObject.Tablename[..^(lastPart.Length + 1)]);
				definition = definition?.GetRevisionDefinition();
			}
		}

		if (definition == null)
			throw new DataStoreOperationException(
				$"Could not find definition for index '{dbObject.Name}' on table '{dbObject.Tablename}' with statement '{dbObject.Statement}'");

		Match match = Regex.Match(dbObject.Statement, """\(\s*([^)]+)\s*\)""");

		if (!match.Success)
		{
			throw new DataStoreOperationException($"Could not get a column name from create index statement: '{dbObject.Statement}'");
		}

		var columnName = match.Groups[1].Value;
		columnName = columnName.Trim('"');
		if (columnName == StorageSystemField.SysFulltext.ToString())
		{
			field = null;
			return definition;
		}

		field = definition.Fields.FirstOrDefault(f => f.Name == columnName) ?? throw new DataStoreOperationException(
					$"Could not find field for column name {columnName} in index '{dbObject.Name}' on table '{dbObject.Tablename}' with statement '{dbObject.Statement}'");
		return definition;
	}
	
	/// <summary>
	/// Changes the following fields:
	/// Definition: Id, LookupSource: FieldName
	/// RevisionDefinition: Id, ElementId
	/// MultivalueLookupDefinition: Id, Name, OtherTable
	/// </summary>
	/// <param name="migration"></param>
	private void ChangeIdColumnsToUuidColumnType(Migration migration)
	{
		_connection.WithDbContext(db =>
		{
			var definitionList = db.StorageIndexDefinition.Include(it => it.Fields).ToList();
			var fulltextDefinitions = definitionList.Where(it => it.FulltextSearch).ToList();

			var views = GetViews();
			BatchExec(migration, views.Select(it => it.Drop));

			foreach (var definition in fulltextDefinitions)
			{
				PostgresMigrator.RemoveDbFulltextSearch(migration, definition.Name);
			}

			var allConstraints = GetConstraints();
			BatchExec(migration, allConstraints.Select(it => it.Drop));

			foreach (var definition in definitionList)
			{
				migration.Alter.Column(StorageSystemField.Id.ToString()).OnTable(definition.Name)
					.AsCustom($"UUID USING \"{StorageSystemField.Id.ToString()}\"::UUID")
					.PrimaryKey();
				definition.Fields.First(it => it.Name == StorageSystemField.Id.ToString()).Type = DataStoreFieldType.Guid;
				if (migration.Schema.Table(definition.RevisionTable).Exists())
				{
					var revisionDefinition = definition.GetRevisionDefinition();
					migration.Alter.Column(StorageSystemField.Id.ToString()).OnTable(revisionDefinition!.Name)
						.AsCustom($"UUID USING \"{StorageSystemField.Id.ToString()}\"::UUID")
						.PrimaryKey();
					migration.Alter.Column(RevisionDefinition.ElementId).OnTable(revisionDefinition.Name)
						.AsCustom($"UUID USING \"{RevisionDefinition.ElementId}\"::UUID");
				}
			}

			foreach (var definition in definitionList)
			{
				foreach (var field in definition.Fields)
				{
					if (field.SystemField)
						continue;

					if (field.LookupSource == null)
						continue;

					if (field.LookupSourceMappingTable == null)
					{
						migration.Alter.Column(field.Name).OnTable(definition.Name)
							.AsCustom($"UUID USING \"{field.Name}\"::UUID");
						field.Type = DataStoreFieldType.Guid;
					}
					else
					{
						migration.Alter.Column(StorageSystemField.Id.ToString()).OnTable(field.LookupSourceMappingTable)
							.AsCustom($"UUID USING \"{StorageSystemField.Id.ToString()}\"::UUID");
						migration.Alter.Column(field.LookupSource).OnTable(field.LookupSourceMappingTable)
							.AsCustom($"UUID USING \"{field.LookupSource}\"::UUID");
						migration.Alter.Column(definition.Name).OnTable(field.LookupSourceMappingTable)
							.AsCustom($"UUID USING \"{definition.Name}\"::UUID");
					}
				}
			}

			db.SaveChanges();

			allConstraints.Reverse();
			BatchExec(migration, allConstraints.Select(it => it.Create));

			foreach (var definition in fulltextDefinitions)
			{
				PostgresMigrator.AddDbFulltextSearch(migration, new PostgresLanguages()[_connection.Db.CustomerContext?.Language.Split("-")[0] ?? "en"],
													 definition.Name, definition.Fields);
			}

			views.Reverse();
			BatchExec(migration, views.Select(it => it.Create));

			return 0;
		});
	}

	private void AddDateTimeToFulltextIndex(Migration migration)
	{
		_connection.WithDbContext(db =>
		{
			var definitionList = db.StorageIndexDefinition.Include(it => it.Fields).ToList();
			var fulltextDefinitions = definitionList.Where(it => it.FulltextSearch).ToList();
			
			var views = GetViews();
			BatchExec(migration, views.Select(it => it.Drop));

			foreach (var definition in fulltextDefinitions)
			{
				PostgresMigrator.RemoveDbFulltextSearch(migration, definition.Name);
			}

			foreach (var definition in fulltextDefinitions)
			{
				PostgresMigrator.AddDbFulltextSearch(migration, new PostgresLanguages()[_connection.Db.CustomerContext?.Language.Split("-")[0] ?? "en"],
													 definition.Name, definition.Fields);
			}
			
			views.Reverse();
			BatchExec(migration, views.Select(it => it.Create));

			return 0;
		});
	}

	private static void BatchExec(Migration migration, IEnumerable<string> statements)
	{
		foreach (var constraints in statements.Chunk(500))
		{
			migration.Execute.Sql(string.Join("\n", constraints));
		}
	}

	private List<DropCreateStatement> GetConstraints()
	{
		return _connection.WithDbContext(db => db.Database.SqlQueryRaw<DropCreateStatement>("""
																			   SELECT 'ALTER TABLE '||nspname||'."'||relname||'" DROP CONSTRAINT "'||conname||'";' AS Drop,
																			          'ALTER TABLE '||nspname||'."'||relname||'" ADD CONSTRAINT "'||conname||'" '|| pg_get_constraintdef(pg_constraint.oid)||';' AS Create
																			   FROM pg_constraint
																			   INNER JOIN pg_class ON conrelid=pg_class.oid
																			   INNER JOIN pg_namespace ON pg_namespace.oid=pg_class.relnamespace
																			   where relname in ((select "Name" from "StorageIndexDefinition") union (select "Name" || '_Revision' from "StorageIndexDefinition"))
																			   ORDER BY CASE WHEN contype='f' THEN 0 ELSE 1 END,contype,nspname,relname,conname
																			   """).ToList());
	}
	
	private List<DropCreateStatement> GetViews()
	{
		return _connection.WithDbContext(db => db.Database.SqlQueryRaw<DropCreateStatement>("""
																			   WITH recursive view_deps AS
																			   (
																			          SELECT v.relname             AS obj,
																			                 orig.relname          AS depends_on,
																			                 1                     AS depth
																			          FROM   pg_catalog.pg_depend  AS d
																			          join   pg_catalog.pg_rewrite AS r
																			          ON     r.oid = d.objid
																			          join   pg_catalog.pg_class AS orig
																			          ON     orig.oid = d.refobjid
																			          join   pg_catalog.pg_class AS v
																			          ON     v.oid = r.ev_class
																			          join   pg_namespace n
																			          ON     n.oid = v.relnamespace
																			          WHERE  v.relkind = 'v'
																			          AND    d.classid = 'pg_rewrite'::regclass
																			          AND    d.refclassid = 'pg_class'::regclass
																			          AND    d.deptype = 'n'
																			          AND    n.nspname NOT IN ('pg_catalog',
																			                                   'information_schema')
																			          UNION ALL
																			          SELECT v.relname     AS obj,
																			                 view_deps.obj AS depends_on,
																			                 depth+1
																			          FROM   pg_catalog.pg_depend  AS d
																			          join   pg_catalog.pg_rewrite AS r
																			          ON     r.oid = d.objid
																			          join   pg_catalog.pg_class AS orig
																			          ON     orig.oid = d.refobjid
																			          join   pg_catalog.pg_class AS v
																			          ON     v.oid = r.ev_class
																			          join   pg_namespace n
																			          ON     n.oid = v.relnamespace
																			          join   view_deps
																			          ON     view_deps.obj = orig.relname
																			          WHERE  v.relkind = 'v'
																			          AND    d.classid = 'pg_rewrite'::regclass
																			          AND    d.refclassid = 'pg_class'::regclass
																			          AND    d.deptype = 'n'
																			          AND    n.nspname NOT IN ('pg_catalog',
																			                                   'information_schema') )
																			   SELECT   'DROP VIEW IF EXISTS "'
																			                     || obj
																			                     || '";' AS Drop,
																			            'CREATE OR REPLACE VIEW "'
																			                     || obj
																			                     || '" AS '
																			                     || pg_get_viewdef('"'
																			                     || obj
																			                     || '"') AS Create
																			   FROM     (
																			                   SELECT *
																			                   FROM   view_deps
																			                   UNION ALL
																			                   SELECT c.relname AS obj,
																			                          '',
																			                          0
																			                   FROM   pg_class c
																			                   join   pg_namespace n
																			                   ON     n.oid = c.relnamespace
																			                   WHERE  c.relkind = 'v'
																			                   AND    n.nspname NOT IN ('pg_catalog',
																			                                            'information_schema') )
																			   GROUP BY obj
																			   ORDER BY max(depth) desc
																			   """).ToList());
	}
}