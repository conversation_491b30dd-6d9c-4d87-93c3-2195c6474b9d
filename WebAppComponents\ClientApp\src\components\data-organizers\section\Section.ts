﻿import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, queryAssignedElements, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { Size } from '@/enums/size.ts'
import { query } from 'lit/decorators/query.js'

export type SectionType = {
	heading: string
	allowCollapse: boolean
	calcHeight: boolean
	collapsed: boolean
	hidden: boolean
	maxHeight?: number
	subtitle?: string
	skeleton: boolean
	ignoreOverflow: boolean
}

const COLLAPSE_ANIMATION_TIME = 300 as const
const MINIMAL_CONTENT_SIZE = 120 as const

/**
 * Section web component using LIT (https://lit.dev)
 */
@customElement('lvl-section')
export class Section extends LitElement implements SectionType {

	// enable animations for vanishing scrollbar
	static {
		const documentStyles = css`
			@property --scrollbar-color {
				syntax: "<color>";
				inherits: true;
				initial-value: transparent;
			}
    `;
		document.adoptedStyleSheets.push(documentStyles.styleSheet!);
	}
	
	/* All css styling must be implemented here OR via 'import' statement
	 * import { inputStyles } from 'styles.ts'
	 * ...
	 * static styles = [inputStyles, css` ... `]
	 */
	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.scrollbar,
		styles.skeleton,
		styles.vanishingScrollbar,
		css`
			:host {
				--content-height: 1fr;
				--content-padding: 1.2rem var(--size-spacing-m) var(--size-spacing-m);
			}
			
			:host([hidden]) {
				display: none;
			}

			:host([collapsed]) {
				--content-height: 0fr;
				
				& .section__header {
					border-bottom-color: transparent;
				}
				
				& .section__content {
					padding-top: 0;
					padding-bottom: 0;
				}
			}

			/* don't show scrollbar during open/close animation (prevent flickering) */
			:host([collapsed]) .section__content::-webkit-scrollbar-thumb,
			:host([opening]) .section__content::-webkit-scrollbar-thumb {
					background-color: transparent;
			}

			:host([ignore-overflow]) {
				--content-overflow: hidden;
			}

			:host([skeleton]) ::slotted(:not([slot])) {
				visibility: hidden;
			}

			:host([borderless]) section {
				border: none;
				box-shadow: none !important;
			}

			section {
				position: relative;
				display: grid;
				align-content: start;
				grid-template-rows: auto var(--content-height);
				transition: grid-template-rows ${COLLAPSE_ANIMATION_TIME}ms ease-in-out;
				background-color: var(--cp-clr-background-lvl-0);
				border-radius: var(--size-radius-m);
				box-shadow: 0 0 0.2rem 0 var(--cp-clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--cp-clr-shadow-weak);

				width: 100%;
				height: fit-content;
				max-height: var(--max-height, 100%);
				padding-bottom: var(--size-spacing-m);
			}

			.section__header {
				display: flex;
				gap: var(--size-spacing-s);
				align-items: center;
				width: calc(100% - var(--size-spacing-l));				
				border-bottom: 1px solid var(--cp-clr-state-active);
				line-height: 3.2rem;
				height: calc(3.2rem + var(--size-spacing-m));
				padding: var(--size-spacing-m) var(--size-spacing-s) 0 var(--size-spacing-m);
				margin: 0 var(--size-spacing-m);
				z-index: 4; /* because of waiting dialog in emb pages which is 3 */
				user-select: none;
				transition: border-bottom-color ${COLLAPSE_ANIMATION_TIME}ms ease-in-out;

				& h2 {
					flex-grow: 1;
					font-weight: normal;
					white-space: nowrap;
					overflow: hidden;

					display: flex;
					align-items: center;
				}

				& .section__title {
					color: var(--cp-clr-text-secondary);
					font-size: var(--size-text-l);
					text-overflow: ellipsis;
					overflow: hidden;
				}

				& .section__subtitle {
					margin-left: var(--size-spacing-m);
					font-size: var(--size-text-m);
					color: var(--cp-clr-text-tertiary);
					width: 0;
					overflow: hidden;
					text-overflow: ellipsis;
					flex-grow: 1;
				}
			}

			/* sticky header while scrolling */
			:host([sticky]) .section__header {
				position: sticky;
				top: 0;
				z-index: 5; /* because of embedded header which is 4 */
				background-color: var(--cp-clr-background-lvl-0);
			}
			
			:host([sticky]) .section__content:not(.skeleton__block)::after {
				content: "";
				position: absolute;
				width: calc(100% - var(--size-spacing-l));
				height: 1px;
				left: var(--size-spacing-m);
				bottom: var(--size-spacing-m);
				background-color: var(--clr-background-lvl-0);
				z-index: 999;
			}

			.section__header__before {
				position: absolute;
				top: -0.4rem;
				left: -1.2rem;
				right: -1.2rem;
				height: 1.2rem;
				background-color: var(--clr-background-lvl-1);
				overflow: hidden;

				&::before {
					content: "";
					position: absolute;
					top: 0.4rem;
					left: 0.4rem;
					right: 0.4rem;
					height: 1.2rem;
					background-color: var(--clr-background-lvl-0);
					border-radius: var(--size-radius-m) var(--size-radius-m) 0 0;
					box-shadow: 0 0 0.2rem 0 var(--cp-clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--cp-clr-shadow-weak);
				}
			}

			.section__content {
				display: flex;
				flex-direction: column;
				gap: var(--size-spacing-m);
				height: fit-content;
				max-height: 100%;
				padding: var(--content-padding);
				overflow: var(--content-overflow, auto);
				transition: padding-top ${COLLAPSE_ANIMATION_TIME}ms ease-in-out;

				&.full-size::slotted(:not([slot])) {
					height: 100% !important;
				}
			}
		`,
	]

	//#region attributes

	@property()
	heading: string = ''

	@property()
	subtitle?: string

	@property({ type: Boolean, attribute: 'allow-collapse', reflect: true })
	allowCollapse: boolean = false

	@property({ type: Boolean, reflect: true })
	get collapsed() {
		return (this._collapsed && this._userCollapsed != false) || this._userCollapsed == true;
	}
	set collapsed(value: boolean) {
		if (this.initDone) {
			this._userCollapsed = value;
			if (this._userCollapsed == this._collapsed)
				localStorage.removeItem(`lvl:component:section:${this.id}:userCollapsed`)
			else
				localStorage.setItem(`lvl:component:section:${this.id}:userCollapsed`, this._userCollapsed ? '1' : '0')
		} else
			this._collapsed = value;
	}
	private _collapsed: boolean = false

	@property({ type: Boolean, reflect: true })
	opening: boolean = false

	@property({ type: Boolean, reflect: true })
	hidden: boolean = false

	@property({ type: Number, attribute: 'max-height' })
	maxHeight?: number

	/**
	 * Should the content of the section take over the scrolling? 
	 * Height of the inner content has to be calculated to use inner scrollbars
	 */
	@property({ type: Boolean, attribute: 'ignore-overflow' })
	ignoreOverflow: boolean = false

	/**
	 * Should the maximum height be calculated based on the main container size?
	 * Produces resize calculations
	 */
	@property({ type: Boolean, attribute: 'calc-height' })
	calcHeight: boolean = false

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false

	@property({ type: Boolean, reflect: true })
	sticky: boolean = false
	
	@property({ type: Boolean, reflect: true })
	initDone: boolean = false

	@property({ type: Boolean, reflect: true })
	borderless: boolean = false

	//#endregion

	//#region states
	
	@state()
	private containerHeight: number = 0
	
	@state()
	private _userCollapsed?: boolean
	
	//#endregion states

	//#region private properties

	@queryAssignedElements()
	private _contentElements?: HTMLElement[]

	@query('.section__content')
	private _contentSlot!: HTMLElement

	private resizeObserver?: ResizeObserver

	private sectionContainer: HTMLElement | null = null

	private containerResizeObserver?: ResizeObserver
	
	private isSubSection: boolean = false

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const subtitle = this.subtitle ? html`<span class="section__subtitle">(${this.subtitle})</span>` : ''
		return html`
			<section class="${this.isSubSection ? 'sub-section' : ''}" style="${this.maxHeight || this.containerHeight ? `--max-height: ${this.maxHeight || this.containerHeight}px` : ''}">
				${this.allowCollapse || this.heading ? html `
				<div class="section__header">
					${this.sticky ? html`<div class="section__header__before"></div>` : ''}
					<h2>
						<span class="section__title">${this.heading}</span>
						${subtitle}
					</h2>
					<slot name="action"></slot>
					${this.allowCollapse ? html`
						<lvl-button class="section__toggle" size="${Size.Medium}" icon="chevron-up" @click="${this.handleCollapseButtonClick}"
												style="${this.collapsed ? '--icon-rotation: 180deg;' : ''}"></lvl-button>` : ''}
				</div>` : ''}
				<slot class="section__content ${this.ignoreOverflow ? '' : 'vanishing-scrollbar'} ${this.skeleton ? 'skeleton__block' : ''}"></slot>
			</section>
		`
	}

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		
		if (this.parentElement?.closest('lvl-section'))
			this.isSubSection = true

		this.initDone = true
		
		const userCollapsed = localStorage.getItem(`lvl:component:section:${this.id}:userCollapsed`)
		if (userCollapsed)
			this.collapsed = userCollapsed == "1"
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)

		if (this._contentSlot && this.ignoreOverflow) {
			this.resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
				if (this.collapsed || !Array.isArray(entries) || !entries.length || this.opening)
					return
				
				window.requestAnimationFrame(() => this.adjustContentHeight())
			})
			this.resizeObserver.observe(this._contentSlot)
		}
		
		// if browser tab is resized, calculate the containerHeight to display the section with the right maximum value
		if (this.calcHeight) {
			this.sectionContainer = this.closest('[data-section-container], body')
			if (this.sectionContainer) {
				this.containerResizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
					if (!Array.isArray(entries) || !entries.length)
						return

					window.requestAnimationFrame(() => this.calculateContainerHeight())
				})
				this.containerResizeObserver.observe(this.sectionContainer)
			}
		}
	}

	protected willUpdate(_changedProperties: PropertyValues) {
		super.willUpdate(_changedProperties)

		if(_changedProperties.has('maxHeight') && this.maxHeight == 0 && this.calcHeight)
			this.calculateContainerHeight()
	}

	protected updated(_changedProperties: PropertyValues) {
		super.updated(_changedProperties)
		
		if(_changedProperties.has('maxHeight') || _changedProperties.has('containerHeight') && this.ignoreOverflow) {
			this.adjustContentHeight()
			
			/*
			 * enable observer which listen on the outer container height if maxHeight was set to 0
			 * disable it otherwise
			 */
			if( _changedProperties.has('maxHeight') && this.calcHeight && this.sectionContainer && this.containerResizeObserver) {
				if(this.maxHeight && this.maxHeight > 0)
					this.containerResizeObserver.unobserve(this.sectionContainer)
				else
					this.containerResizeObserver.observe(this.sectionContainer)
			}
		}
		
		if(_changedProperties.get('collapsed') != null && !this.collapsed && this.ignoreOverflow) {
			this.opening = true
			setTimeout(() => {
				this.adjustContentHeight()
				this.opening = false
			}, COLLAPSE_ANIMATION_TIME)
		}
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this.resizeObserver?.disconnect()
		this.containerResizeObserver?.disconnect()
	}

	//#endregion

	//#region public methods
	
	public async adjustContentHeight() {
		if (this._contentElements?.length != 1)
			return

		// save scrollTop of the element to reset it after calculation
		const queryViewContainer = this.querySelector<LitElement>('lvl-table, lvl-gallery')
		let scrollPosition = 0
		
		// wait for inner component to be finished
		if (queryViewContainer) {
			await customElements.whenDefined(queryViewContainer.localName)
			await queryViewContainer.updateComplete
			scrollPosition = queryViewContainer?.scrollTop ?? 0
		}

		// let the content give the full space to check how much height it really needs
		this._contentSlot.classList.add('full-size')
		const style = window.getComputedStyle(this._contentSlot)
		const calculatedSlotHeight = Math.max(MINIMAL_CONTENT_SIZE, this._contentSlot.clientHeight - (parseFloat(style.paddingTop) + parseFloat(style.paddingBottom)))
		
		// give the content the calculated height	
		this._contentElements[0].style.height = calculatedSlotHeight > 0 ? `${calculatedSlotHeight}px` : '100%'
		this._contentSlot.classList.remove('full-size')

		// reset the scroll position
		if(queryViewContainer && customElements.get(queryViewContainer.localName))
			queryViewContainer.scrollTop = scrollPosition
	}
	
	//#endregion

	//#region private methods

	private handleCollapseButtonClick(event: MouseEvent) {
		this.collapsed = !this.collapsed
		event.stopPropagation()
	}
	
	private calculateContainerHeight() {
		if(!this.sectionContainer)
			return
		
		const style = window.getComputedStyle(this.sectionContainer)
		const padding = Math.max(16, parseFloat(style.paddingTop) + parseFloat(style.paddingBottom))
		this.containerHeight = this.sectionContainer?.clientHeight ? this.sectionContainer?.clientHeight - padding : 0
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-section': Section
	}
}