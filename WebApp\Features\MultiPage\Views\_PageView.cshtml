@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Microsoft.OpenApi.Extensions
@inject IAssetService AssetService
@inject IExtendedStringLocalizerFactory LocalizerFactory
@model Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels.MultiDataModel
@{
	var localizer = LocalizerFactory.Create("MultiPage", "");
	var viewId = Guid.NewGuid();
	var displayViews = Model.Page.Views?.Where(view => Model.GridViewPage != null || view.Display == true).OrderBy(view => view.NameTranslated ?? view.TypeName).ToList() ?? [];
	IList<SelectListItemDefinition>? viewList = displayViews
		.Select(view => new SelectListItemDefinition(view.NameTranslated ?? view.TypeName, view.Id.ToString() ?? "", view.Icon))
		.ToList();
	var multiPageName = Model.Embedded ? $"grid-view-page_{Model.GridViewPage?.Id}" : $"page_{Model.Page.Id}";
}
<style>
	.page-view__wrapper .page-view:not([data-open]) {
		display: none;
	}
</style>
<script type="module" defer>
	import { initPageViewRendering } from '@AssetService.SolvePath("/page/visualization.ts")'
	
	const multiView = document.getElementById('multi-view-@(viewId)')
	const actionBar = document.getElementById('action-bar-@(viewId)')
	
	@if (Model.Embedded)
	{
		<text>
			initPageViewRendering(multiView, '@Model.GridViewPage?.EmbeddedPageId', { embedded: true,  embeddedPageId: '@Model.GridViewPage?.Id', currentViewId: '@Model.GridViewPage?.EmbeddedViewId', actionBar: actionBar, multiView: multiView })
		</text>
	}
	else
	{
		<text>
			initPageViewRendering(multiView, '@Model.Page.Id', { currentViewId: '@Model.Page.DefaultViewId', embeddedPageId: '@Model.GridViewPage?.Id', actionBar: actionBar, multiView: multiView })
		</text>
	}
	
	// switch between views
	actionBar.addEventListener('action-view:click', event => {
		const viewId = event.detail.origin.value
		const newView = multiView.querySelector(`.page-view[data-view-id='${viewId}']`)
		if (!newView) {
			console.error(`View could not be changed. Id: ${viewId} was not found`)
			return
		}
		newView.dataset.open = true
	})
	
	@if (Model.Page.DetailPageSlug != null)
	{
		<text>
			// append row click event handler
			const pageUrl = '/Public/Pages/@(Model.Page.DetailPageSlug)'
			const apiUrl = '/Api/DataSources/@Model.Page.DataSourceId/Elements/##elementId##'
			multiView.onRowClick = async (record, _, newTab) => {
				const detailPageUrl = pageUrl+"/"+record.data.Id
				if (newTab) {
					Page.openNewTab(detailPageUrl, true)
					return
				}
				
				@if (Model.Embedded)
				{
					<text>
						// define callbacks for resolve (and cancel)
						const callback = () => {
							multiView.onRowClick(record, _, newTab)
						}

						// define event and put callbacks in detail
						const pageLeaveEvent = new CustomEvent('initiate-page-leave', {
							cancelable: true,
							detail: {
								resolveCallback: callback
							}
						})

						// dispatch the page-leave-event and stop if it gets canceled
						if(!window.dispatchEvent(pageLeaveEvent))
							return
					
					</text>
				}
			
				// clear old form data (otherwise form may render with wrong information)
				Page.resetFormData()
				let elementIds = multiView.items.filter(row => row.data != null).map(row => row.id)
				let index = elementIds.indexOf(record.id)
				const offset = Math.floor(index/50) * 50
				
				index = index - offset
				elementIds = elementIds.slice(offset, offset+50)
				
				const navigationOptions = { elementIds: elementIds, index: index, navigationOffset: offset, filters: multiView.filters ?? [], sortings: multiView.sorting ?? [], listUrl: multiView.url, count:  multiView.totalCount }
				const loadPage = Page.load(pageUrl, {}, { pageOptions : { navigationOptions: navigationOptions } })
				const loadData = Page.getJSON(apiUrl.replace('##elementId##', record.data.Id))

				// wait for page to load and update url afterwards
				await loadPage			
				Page.setInfo(detailPageUrl)
				Page.setMainPage(detailPageUrl)

				let json = await loadData
				if (json.data) {
					Page.saveFormData(json.data.values, { favorite: json.data.isFavourite, inactive: json.data.isInactive, dataSourceId: '@(Model.Page.DataSourceId)' })
					@if (Model.Page.DataSource?.Inactive ?? false)
					{
						@: Page.buttonConfig.displayDiscardToggle(json.data.isInactive, '@Model.Page.DataSourceId')
					}
				}

				if (json.data?.fileInfo)
					Page.saveFileInfo(json.data.fileInfo)
				
				if (json.data?.deepZoomInfo && json.data.deepZoomInfo.state !== 'Failed')
					Page.setBlueprintConfig({
						imageInfo: {
							width: json.data.deepZoomInfo.width,
							height: json.data.deepZoomInfo.height,
							tileSize: json.data.deepZoomInfo.tileSize,
							elementId: record.data.Id,
							renderState: json.data?.deepZoomInfo.state
						},
						pinSource: json.data?.annotationSourceInfo
					})
				
				const form = document.getElementById('page-detail')?.querySelector("lvl-form")
				if (form) {
					await Page.setFormData(form, Page.getFormData())
					const noEntriesPlaceholder = document.getElementById('noEntriesPlaceholder')
					const viewer = document.querySelector('lvl-viewer.file-viewer')
					if (viewer) {
						viewer.showPlaceholder = !Page.hasFile()
						if (Page.hasFile() && !viewer.fileId) {
							const fileInfo = Page.getFileInfo()
							const blueprintConfig = Page.getBlueprintConfig()
							if (blueprintConfig) {
								viewer.deepzoomImageInfo = blueprintConfig.imageInfo
								viewer.pinSource = blueprintConfig.pinSource
								viewer.annotationCreate = async (defaultValues) => {
									return await FileManager.showCreatePage({
										target: document.querySelector('body'),
										dataSourceId: blueprintConfig.pinSource.id,
										createPageId: blueprintConfig.pinSource.createPageId,
										defaultValues: defaultValues,
										embedded: true,
										allowFile: blueprintConfig.pinSource.allowFile === true
									})
								}
							}
							viewer.classList.add('enabled')
							viewer.loadNewDocument(`@(Model.Page.DataSourceId)`, fileInfo.id, fileInfo.name.toString().substring(fileInfo.name.toString().lastIndexOf('.') + 1))
							noEntriesPlaceholder?.classList?.add('hide')
						} else
							noEntriesPlaceholder?.classList?.remove('hide')
					}
					form.skeleton = false
				}
			}
		</text>
	}

	@if (Model.Embedded)
	{
		<text>
			// add event listener to set the record count
			const embeddedWrapper = multiView.closest('.embedded_section')
			if (embeddedWrapper) {
				multiView.addEventListener('query-view:changed', (_) => {
					embeddedWrapper.subtitle = multiView.totalCount
					embeddedWrapper.adjustContentHeight()
				})
			}
		</text>
	}
	else
	{
		<text>
			// wait for response and update the filter count
			multiView.addEventListener('query-view:changed', () => {
				const filterPanel = document.getElementById('page-@(Model.Page.Id)-filter')
				if (filterPanel)
					filterPanel.count = multiView.totalCount ?? 0
			})
		</text>
	}

	@if (Model.ParentElementId != null)
	{
		<text>
			// only load element if we don't already know it
			if (Page.getFormData()["Id"] !== '@Model.ParentElementId') {
				const json = await Page.getJSON('/Api/DataSources/@Model.ParentPage?.DataSourceId/Elements/@Model.ParentElementId')
				if (json.data)
					Page.saveFormData(json.data.values, { favorite: json.data.isFavourite, inactive: json.data.isInactive })
			}
		</text>
	}

	// set filter & url
	let multiViewFilters = []
	@if (Model.GridViewPage?.KeyField != null)
	{
		var referenceFieldName = Model.GridViewPage.ReferenceType == GridViewPageReferenceType.ForeignElement ? Model.GridViewPage.ReferenceField?.Name : null;
		<text>
			multiViewFilters.push({
				filterColumn: '@(Model.GridViewPage?.KeyField?.Name).Id',
				operator : '@QueryParamFilterOperator.Equals.GetDisplayName()',
				compareValue: @(Html.Raw(Model.Embedded ? $"'##{referenceFieldName ?? "Id"}##'" : referenceFieldName != null ? $"Page.getFormData()['{referenceFieldName}']['id']" : "Page.getFormData()['Id']")),
				force: true,
			})
		</text>
	}

	// add additional filters
	@if (Model.GridViewPage?.Filters.Count > 0)
	{
		@foreach (var filter in Model.GridViewPage.Filters)
		{
			var compareValue = filter.CompareValue ?? "";
			<text>
                multiViewFilters.push({
					filterColumn: '@(filter.FilterFieldType == DataType.Guid ? filter.FilterFieldName+".Id" : filter.FilterFieldName)',
					operator : '@filter.Operator',
					compareValue: @(Html.Raw(Model.Embedded ? $"'{compareValue}'" : $"Page.replacePlaceholders('{compareValue}', true)")),
	                force: true
				})
			</text>
		}
	}
	
	const savedUserQueryParams = Page.restorePageOptionQueryParameters('@multiPageName')
	
	// Is there a filter panel? Pass forced filters to it
	const filterPanel = document.getElementById('page-@(Model.Page.Id)-filter')
	if (filterPanel) {
		await Component.waitForComponentInitialization(filterPanel)
		filterPanel.forcedFilters = multiViewFilters
		filterPanel.userFilters = savedUserQueryParams.filters
		
		// wait until toggle and input animations are done
		setTimeout(() => filterPanel.skeleton = false, 300)
		
		multiView.filters = filterPanel.filters
	} else
		multiView.filters = [ ...multiViewFilters, ...savedUserQueryParams.filters ]
		
	// Change sorting if there is a saved user sorting
	if (savedUserQueryParams.sorting.length > 0)
		multiView.sorting = savedUserQueryParams.sorting

	multiView.addEventListener('filter:changed', () => {
		Page.storePageOptionFilters('@multiPageName', multiView.filters)
	})

	multiView.addEventListener('sorting:changed', () => {
		Page.storePageOptionSorting('@multiPageName', multiView.sorting)
	})

	// set always as last one to prevent it from displaying wrong elements
	multiView.url="/Api/DataSources/@(Model.Page.DataSourceId)/Elements"
</script>
<query-view-action-bar-component id="action-bar-@(viewId)" embedded="@(Model.Embedded)" with-select-all="true" with-favorite-action="true" with-inactive-action="true" with-create="@(!Model.Embedded && Model.Page.CreatePageId != null)" with-export="@(!Model.Embedded)" with-import="@(!Model.Embedded)" with-display-type views="@(viewList)">
</query-view-action-bar-component>
<div class="page-view__wrapper">
	<multi-data-view-component id="multi-view-@(viewId)" sorting="@Model.GetQueryParamSorting()" embedded="@Model.Embedded" height="100%" width="100%" limit="50" request-key="@viewId">
		@foreach (var view in displayViews)
		{
			<section class="page-view" data-view-id="@view.Id" data-view-type="@view.Type"></section>
		}
	</multi-data-view-component>
</div>