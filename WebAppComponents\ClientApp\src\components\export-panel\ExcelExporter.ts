import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state, query } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/shared/communication-service.ts'
import { v4 as uuid } from 'uuid'

export type ColumnData = {
	key: string
	display: string
	fieldId?: string
	id?: string
	dataType?: string
	columnOrder?: number
}

export type ExportPresetData = {
	label: string
	value: string
	status: boolean
}

export type ExcelExporterType = {
	dataSourceId?: string
	viewId?: string
	pageDataSourceId?: string
	visibleColumns: ColumnData[]
	additionalColumns: ColumnData[]
	exportPresets: ExportPresetData[]
	selectedItems?: any[]
	onExportStart?: () => void
	onExportComplete?: (success: boolean) => void
}

/**
 * Excel Exporter web component using LIT (https://lit.dev)
 */
@customElement('lvl-excel-exporter')
export class ExcelExporter extends LitElement implements ExcelExporterType {

	static styles = [
		styles.base,
		styles.color,
		fontAwesome,
		css`
			:host {
				display: block;
			}

			/* Additional styles to ensure proper section rendering */
			lvl-section {
				--section-box-shadow: none !important;
			}

			/* Excel Exporter Dialog Styles */
			.excel-exporter-dialog {
				display: block;
				--width: 45rem;
			}

			.excel-exporter-dialog-wide {
				display: block;
				--width: 60rem;
			}

			.excel-exporter-dialog-warning {
				display: block;
				--width: 50rem;
			}

			.excel-exporter-dialog-content {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 1rem;
				margin-left: 1.4rem;
				margin-top: 1.4rem;
				margin-bottom: 1.4rem;
				border: 0.1rem solid var(--clr-border);
				border-radius: var(--size-radius-m);
				background-color: var(--clr-background-lvl-0);
			}

			.excel-exporter-preset-content {
				display: flex;
				flex-direction: column;
				padding: 1rem;
				margin-left: 1.4rem;
				margin-top: 1.4rem;
				margin-bottom: 1.4rem;
				border: 0.1rem solid var(--clr-border);
				border-radius: var(--size-radius-m);
				background-color: var(--clr-background-lvl-0);
			}

			.excel-exporter-warning-content {
				display: flex;
				align-items: center;
				width: 100%;
				justify-content: flex-start;
			}

			.excel-exporter-warning-text {
				font-size: 1.4rem;
			}

			.excel-exporter-info-section {
				display: flex;
				align-items: center;
				margin-bottom: 2rem;
				width: 100%;
				justify-content: flex-start;
				color: var(--clr-text-secondary) !important;
			}

			.excel-exporter-info-icon {
				margin-right: 1rem;
				color: var(--clr-text-secondary-positiv);
				font-size: 1rem;
			}

			.excel-exporter-info-text {
				color: var(--clr-text-secondary-positiv);
				font-size: 1rem;
			}

			.excel-exporter-columns-container {
				display: flex;
				gap: 2rem;
				width: 100%;
			}

			.excel-exporter-column-section {
				max-height: 100%;
				height: max-content;
				flex: 1;
			}

			.excel-exporter-column-buttons {
				display: flex;
				justify-content: space-between;
			}

			.excel-exporter-column-list-container {
				overflow: auto;
				scrollbar-color: #ccc var(--clr-background-lvl-0);
			}

			.excel-exporter-column-list {
				width: 100%;
				height: 100%;
			}

			.excel-exporter-hidden {
				display: none;
			}
		`,
	]

	//#region attributes

	@property({ attribute: 'data-source-id' })
	dataSourceId?: string

	@property({ attribute: 'view-id' })
	viewId?: string

	@property({ attribute: 'page-data-source-id' })
	pageDataSourceId?: string

	@property({ type: Array })
	visibleColumns: ColumnData[] = []

	@property({ type: Array })
	additionalColumns: ColumnData[] = []

	@property({ type: Array })
	exportPresets: ExportPresetData[] = []

	@property({ type: Array })
	selectedItems?: any[]

	@property({ type: Function })
	onExportStart?: () => void

	@property({ type: Function })
	onExportComplete?: (success: boolean) => void

	@property({ attribute: 'multi-view-selector' })
	multiViewSelector?: string = 'lvl-multi-data-view'

	@property({ attribute: 'data-source-selector' })
	dataSourceSelector?: string = 'lvl-data-source'

	@property({ attribute: 'paging-summary-selector' })
	pagingSummarySelector?: string = 'span#paging-summary'



	//#endregion

	//#region states

	@state()
	private _showWarningDialog = false

	@state()
	private _showPresetDialog = false

	@state()
	private _showColumnSelectionDialog = false

	@state()
	private _warningRecordCount = 0

	@state()
	private _warningHasPresets = false

	//#endregion

	//#region query selectors

	@query('lvl-selectable-cards')
	private _selectableCards?: HTMLElement

	//#endregion

	//#region private properties

	private static readonly _localizer: StringLocalizer = new StringLocalizer('ExcelExporter')
	private _abortController?: AbortController
	private _optionSelectedHandler?: EventListener
	private _componentClickHandlers: EventListener[] = []
	private _componentEventListeners: { element: Element | Document, event: string, handler: EventListener }[] = []

	private localize(key: string, ...replacements: any[]): string {
		return ExcelExporter._localizer.localize(key, ...replacements)
	}

	//#endregion

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()
		this._abortController = new AbortController()
	}

	disconnectedCallback() {
		super.disconnectedCallback()
		this._abortController?.abort()

		// Clean up all event listeners
		this._cleanupOptionSelectedHandler()

		// Clean up component-scoped event listeners
		this._cleanupComponentEventListeners()

		// Close any open dialogs
		this._showWarningDialog = false
		this._showPresetDialog = false
		this._showColumnSelectionDialog = false
	}

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)
		this.initializeColumns()
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {

		return html`
			<div>
				<!-- Excel Exporter Component - Use startExport() method to begin export process -->
				<slot></slot>
			</div>

			<!-- Warning Dialog -->
			<lvl-dialog
				class="excel-exporter-dialog-warning"
				headerColor="warning"
				heading="${this.localize('unusuallyHighExports')}"
				icon="warning"
				name="warning-dialog"
				width="450"
				?open="${this._showWarningDialog}">
				<div class="excel-exporter-dialog-content">
					<div class="excel-exporter-warning-content">
						<span class="excel-exporter-warning-text">${this.localize('largeExportWarning', `<strong>${this._warningRecordCount}</strong>`)}</span>
					</div>
				</div>
				<lvl-button slot="button-left" data-action="cancel" color="info"
					label="${this.localize('no')}" @click="${this._handleWarningCancel}"></lvl-button>
				<lvl-button slot="button-right" data-action="proceed" type="primary"
					label="${this.localize('yes')}" @click="${this._handleWarningProceed}"></lvl-button>
			</lvl-dialog>

			<!-- Preset Dialog -->
			<lvl-dialog
				class="excel-exporter-dialog"
				heading="${this.localize('exportPresetHeading')}"
				icon="file-export"
				name="preset-dialog"
				width="450"
				?open="${this._showPresetDialog}">
				<div class="excel-exporter-preset-content">
					<div class="excel-exporter-info-section">
						<i class="fas fa-info-circle excel-exporter-info-icon"></i>
						<span class="excel-exporter-info-text">${this.localize('pickExportPreset')}</span>
					</div>
					<lvl-selectable-cards options='${JSON.stringify(this.exportPresets).replace(/'/g, "&#39;")}'></lvl-selectable-cards>
				</div>
				<lvl-button slot="button-left" data-action="cancel" color="info" initdone
					label="${this.localize('cancel')}" @click="${this._handlePresetCancel}"></lvl-button>
				<lvl-button slot="button-right" data-action="next" type="primary" initdone
					label="${this.localize('next')}" @click="${this._handlePresetNext}"></lvl-button>
			</lvl-dialog>

			<!-- Column Selection Dialog -->
			<lvl-dialog
				class="excel-exporter-dialog-wide"
				heading="${this.localize('exportColumnsHeading')}"
				icon="file-export"
				name="column-selection-dialog"
				width="600"
				?open="${this._showColumnSelectionDialog}">
				<div class="excel-exporter-dialog-content">
					<div class="excel-exporter-info-section">
						<i class="fas fa-info-circle excel-exporter-info-icon"></i>
						<span class="excel-exporter-info-text">${this.localize('selectColumnsInfo')}</span>
					</div>
					<div class="excel-exporter-columns-container">
						<lvl-section class="excel-exporter-column-section" heading="${this.localize('visibleFields')} (${(this.visibleColumns || []).length})" allow-collapse max-height="250" calc-height ignore-overflow borderless>
							<div class="excel-exporter-column-buttons" data-target="visible-columns-btns">
								<lvl-button size="small" color="info" label="${this.localize('selectAll')}" data-action="select-all" data-target="visible-columns-list" ?disabled="${(this.visibleColumns || []).length === 0}" @click="${this._handleSelectAll}"></lvl-button>
								<lvl-button size="small" color="info" label="${this.localize('deselectAll')}" data-action="deselect-all" data-target="visible-columns-list" class="excel-exporter-hidden" @click="${this._handleDeselectAll}"></lvl-button>
								<lvl-button size="small" color="info" icon="search" data-target="visible-columns-search" ?disabled="${(this.visibleColumns || []).length === 0}" @click="${this._handleSearchToggle}"></lvl-button>
							</div>
							<lvl-search rounded="" size="small" data-target="visible-columns-search-bar" style="display: none;"></lvl-search>
							<div class="excel-exporter-column-list-container">
								<lvl-list size="small" id="visible-columns-list" .rows="${(this.visibleColumns || []).map(col => ({name: col.display}))}" class="excel-exporter-column-list">
									<lvl-list-column type="select"></lvl-list-column>
									<lvl-list-data-column name="name" label="${this.localize('columns')}" hide-label></lvl-list-data-column>
								</lvl-list>
							</div>
						</lvl-section>

						<lvl-section class="excel-exporter-column-section" heading="${this.localize('additionalInformation')} (${(this.additionalColumns || []).length})" allow-collapse max-height="250" calc-height ignore-overflow borderless>
							<div class="excel-exporter-column-buttons" data-target="info-columns-btns">
								<lvl-button size="small" color="info" label="${this.localize('selectAll')}" data-action="select-all" data-target="additional-info-list" ?disabled="${(this.additionalColumns || []).length === 0}" @click="${this._handleSelectAll}"></lvl-button>
								<lvl-button size="small" color="info" label="${this.localize('deselectAll')}" data-action="deselect-all" data-target="additional-info-list" class="excel-exporter-hidden" @click="${this._handleDeselectAll}"></lvl-button>
								<lvl-button size="small" color="info" data-target="info-columns-search" icon="search" ?disabled="${(this.additionalColumns || []).length === 0}" @click="${this._handleSearchToggle}"></lvl-button>
							</div>
							<lvl-search rounded="" size="small" data-target="info-columns-search-bar" style="display: none;"></lvl-search>
							<div class="excel-exporter-column-list-container">
								<lvl-list size="small" id="additional-info-list" .rows="${(this.additionalColumns || []).map(col => ({name: col.display}))}" class="excel-exporter-column-list">
									<lvl-list-column type="select"></lvl-list-column>
									<lvl-list-data-column name="name" label="${this.localize('columns')}" hide-label></lvl-list-data-column>
								</lvl-list>
							</div>
						</lvl-section>
					</div>
				</div>
				<lvl-button slot="button-left" data-action="cancel" color="info" initdone
					label="${this.localize('cancel')}" @click="${this._handleColumnSelectionCancel}"></lvl-button>
				<lvl-button slot="button-right" data-action="export-proceed" type="primary" initdone
					label="${this.localize('export')}" @click="${this._handleColumnSelectionExport}"></lvl-button>
			</lvl-dialog>
		`
	}

	//#region public methods

	/**
	 * Start the export process
	 * Follows the exact flow from the original _ExcelExport.cshtml
	 */
	async startExport(): Promise<void> {
		try {
			const hasPresets = await this.loadExportPresets()
			const recordCount = this.getRecordCount()
			const hasSelectedItems = this.hasSelectedItems()

			// Check if we need to show warning dialog for large exports (>1000 records)
			// But skip warning if user has manually selected specific items
			if (recordCount > 1000 && !hasSelectedItems) {
				this.showWarningDialog(recordCount, hasPresets)
			} else if (hasPresets) {
				// If records < 1000 or items are selected, and we have presets, show preset dialog directly
				this.showPresetDialog()
			} else {
				// No presets available and (records < 1000 or items selected), show column selection dialog directly
				await this.showColumnSelectionDialog()
			}
		} catch (error) {
			this.showErrorToast(this.localize('exportErrorGeneric'))
		}
	}

	//#endregion

	//#region private methods

	private async initializeColumns(): Promise<void> {
		// If columns are already provided as props (e.g., in Storybook), use them
		if (this.visibleColumns.length > 0 || this.additionalColumns.length > 0) {
			return
		}

		if (!this.viewId) {
			this.viewId = this._findViewId()
		}

		if (this.viewId) {
			try {
				const columns = await this.fetchColumns()
				this.visibleColumns = columns.visibleColumns
				this.additionalColumns = columns.invisibleColumns
			} catch (error) {
				// Don't fall back to hardcoded columns if API fails - keep empty arrays
				this.visibleColumns = []
				this.additionalColumns = []
			}
		} else {
			// Only use fallback columns in true Storybook environment
			if (this._isStorybookEnvironment()) {
				this.visibleColumns = [
					{ key: 'name', display: 'Name', fieldId: 'field1', dataType: 'string' },
					{ key: 'email', display: 'Email', fieldId: 'field2', dataType: 'string' },
					{ key: 'age', display: 'Age', fieldId: 'field3', dataType: 'number' }
				]
				this.additionalColumns = [
					{ key: 'active', display: 'Active Status', fieldId: 'field4', dataType: 'boolean' },
					{ key: 'created', display: 'Created Date', fieldId: 'field5', dataType: 'datetime' },
					{ key: 'department', display: 'Department', fieldId: 'field6', dataType: 'string' },
					{ key: 'salary', display: 'Salary', fieldId: 'field7', dataType: 'number' },
					{ key: 'lastLogin', display: 'Last Login', fieldId: 'field8', dataType: 'datetime' },
					{ key: 'phone', display: 'Phone Number', fieldId: 'field9', dataType: 'string' },
					{ key: 'address', display: 'Address', fieldId: 'field10', dataType: 'string' }
				]
			} else {
				// In production, keep empty arrays if no viewId found
				this.visibleColumns = []
				this.additionalColumns = []
			}
		}
	}

	private _findViewId(): string | undefined {
		// Strategy 1: Emit event to request view ID from parent
		let viewId: string | undefined
		const viewIdEvent = new CustomEvent('request-view-id', {
			detail: { callback: (id: string) => { viewId = id } },
			bubbles: true,
			composed: true
		})
		this.dispatchEvent(viewIdEvent)

		if (viewId) {
			return viewId
		}

		// Strategy 2: Search in parent hierarchy
		let parent = this.parentElement
		while (parent && !viewId) {
			// Check for various view ID attributes
			const viewIdAttrs = ['data-view-id', 'view-id', 'data-view', 'viewid']
			for (const attr of viewIdAttrs) {
				if (parent.hasAttribute(attr)) {
					viewId = parent.getAttribute(attr) || undefined
					if (viewId) {
						return viewId
					}
				}
			}

			parent = parent.parentElement
		}

		// Strategy 3: Search for page view sections in document
		const pageViewSelectors = [
			'section.page-view[data-view-id]',
			'section[data-view-type="List"][data-view-id]',
			'[data-view-id]',
			'.page-view[data-view-id]'
		]

		for (const selector of pageViewSelectors) {
			const element = document.querySelector(selector)
			if (element) {
				viewId = element.getAttribute('data-view-id') || undefined
				if (viewId) {
					return viewId
				}
			}
		}

		// Strategy 4: Search in multi-view components
		const multiViews = document.querySelectorAll('lvl-multi-data-view')
		for (const multiView of multiViews) {
			const viewIdAttrs = ['data-view-id', 'view-id', 'data-view', 'viewid']
			for (const attr of viewIdAttrs) {
				if (multiView.hasAttribute(attr)) {
					viewId = multiView.getAttribute(attr) || undefined
					if (viewId) {
						return viewId
					}
				}
			}
		}

		return undefined
	}

	private _isStorybookEnvironment(): boolean {
		// Check if we're in Storybook environment
		return typeof window !== 'undefined' &&
			   (window.location.href.includes('storybook') ||
				window.location.href.includes('localhost:6006') ||
				(window as any).__STORYBOOK_ADDONS_MANAGER__ !== undefined)
	}

	private async fetchColumns(): Promise<{ visibleColumns: ColumnData[], invisibleColumns: ColumnData[] }> {
		try {
			if (!this.viewId) {
				return { visibleColumns: [], invisibleColumns: [] }
			}

			// Use allowParallel and disable debouncing to prevent request cancellation
			const response = await CommunicationServiceProvider.get(`/Api/PageViews/${this.viewId}/Columns`, {
				allowParallel: true,
				debouncing: false,
				requestKey: `excel-exporter-columns-${this.viewId}`
			})

			// Handle aborted requests by retrying once
			if (response.state === CommunicationResponseType.Aborted) {
				const retryResponse = await CommunicationServiceProvider.get(`/Api/PageViews/${this.viewId}/Columns`, {
					allowParallel: true,
					debouncing: false,
					requestKey: `excel-exporter-columns-retry-${this.viewId}-${Date.now()}`
				})

				if (retryResponse.state !== CommunicationResponseType.Ok) {
					throw new Error(`API request failed with status ${retryResponse.state}`)
				}

				return this.processColumnsResponse(retryResponse.data)
			}

			if (response.state !== CommunicationResponseType.Ok) {
				throw new Error(`API request failed with status ${response.state}`)
			}

			return this.processColumnsResponse(response.data)
		} catch (error) {
			return { visibleColumns: [], invisibleColumns: [] }
		}
	}

	private processColumnsResponse(data: any): { visibleColumns: ColumnData[], invisibleColumns: ColumnData[] } {
		// Handle the actual API response format
		if (data && data.visibleColumns && data.invisibleColumns) {
			// Transform the data to match our ColumnData interface
			const visibleColumns = data.visibleColumns.map((col: any) => ({
				key: col.key,
				display: col.display,
				fieldId: col.fieldId,
				dataType: col.fieldType
			}))

			const invisibleColumns = data.invisibleColumns.map((col: any) => ({
				key: col.key,
				display: col.display,
				fieldId: col.fieldId,
				dataType: col.fieldType
			}))

			return { visibleColumns, invisibleColumns }
		} else {
			return { visibleColumns: [], invisibleColumns: [] }
		}
	}

	private async loadExportPresets(): Promise<boolean> {
		try {
			// If presets are already provided as props (e.g., in Storybook), use them
			if (this.exportPresets && this.exportPresets.length > 0) {
				return true
			}

			this.showOverlay(this.localize('loadingAvailablePresets'))

			const dataSourceId = this.getDataSourceId()
			const apiUrl = `/Api/ExcelPresets/${dataSourceId}/PerDataSource`

			const response = await CommunicationServiceProvider.get(apiUrl)

			// Let's also make a direct fetch to see the raw server response
			let rawServerData: any = null
			try {
				const rawResponse = await fetch(apiUrl)
				rawServerData = await rawResponse.json()
			} catch (e) {
				// Ignore fetch errors for raw response
			}

			if (response.state !== CommunicationResponseType.Ok) {
				return false
			}

			// Handle the case where server returns data directly (not wrapped in LevelResponse)
			// If CommunicationServiceProvider.data is undefined but we have raw server data, use that
			let presetsData = response.data

			if (!presetsData && rawServerData && Array.isArray(rawServerData)) {
				presetsData = rawServerData
			}

			// Only show preset dialog if server returns actual presets
			if (presetsData && Array.isArray(presetsData) && presetsData.length > 0) {

				const formattedPresets = presetsData.map((item: any) => ({
					label: item.name.replace(/'/g, ""),
					value: item.id,
					status: item.isActive
				}))

				// Add the "Custom" option when we have server presets
				formattedPresets.push({
					label: "Custom",
					value: "custom",
					status: true
				})

				this.exportPresets = formattedPresets
				return true
			}

			// No server presets found, don't show preset dialog
			return false
		} catch (error) {
			return false
		} finally {
			this.hideOverlay()
		}
	}

	private getDataSourceId(): string {
		// Try multiple sources for the data source ID
		let dataSourceId = this.dataSourceId || this.pageDataSourceId

		if (!dataSourceId) {
			// Emit event to request data source ID from parent
			const dataSourceEvent = new CustomEvent('request-data-source-id', {
				detail: { callback: (id: string) => { dataSourceId = id } },
				bubbles: true,
				composed: true
			})
			this.dispatchEvent(dataSourceEvent)

			// Fallback: try to find in closest parent elements
			if (!dataSourceId) {
				let parent = this.parentElement
				while (parent && !dataSourceId) {
					if (parent.hasAttribute('data-source-id') || parent.hasAttribute('data-id')) {
						dataSourceId = parent.getAttribute('data-source-id') || parent.getAttribute('data-id') || ''
						break
					}
					parent = parent.parentElement
				}
			}
		}

		return dataSourceId || ''
	}

	private getRecordCount(): number {
		let recordCount = 100 // default fallback
		// Strategy 1: Search in parent hierarchy
		let parent = this.parentElement
		while (parent) {
	
			if (parent.tagName.toLowerCase() === 'lvl-multi-data-view') {
				recordCount = this._extractRecordCountFromElement(parent)
				if (recordCount > 100) break
			}
			parent = parent.parentElement
		}

		// Strategy 2: Search in the entire document if not found in parents
		if (recordCount === 100) {
			const multiViews = document.querySelectorAll('lvl-multi-data-view')

			for (const multiView of multiViews) {
				recordCount = this._extractRecordCountFromElement(multiView)
				if (recordCount > 100) break
			}
		}

		// Strategy 3: Look for paging summary elements
		if (recordCount === 100) {
			const pagingSummarySelectors = [
				'span#paging-summary',
				'[id*="paging"]',
				'[class*="paging"]',
				'[class*="summary"]',
				'.pagination-info',
				'.record-count'
			]

			for (const selector of pagingSummarySelectors) {
				const element = document.querySelector(selector)
				if (element?.textContent) {
					recordCount = this._extractRecordCountFromText(element.textContent)
					if (recordCount > 100) break
				}
			}
		}

		// Strategy 4: Emit event to request record count from parent (synchronous callback)
		const recordCountEvent = new CustomEvent('request-record-count', {
			detail: {
				callback: (count: number) => {
					if (typeof count === 'number' && count > 0) {
						recordCount = count
					}
				}
			},
			bubbles: true,
			composed: true
		})
		this.dispatchEvent(recordCountEvent)
		return recordCount
	}

	private _extractRecordCountFromElement(element: Element): number {
		const multiView = element as any

		// Try multiple possible property names for record count
		const possibleCountProperties = [
			'totalRecords', 'totalCount', 'recordCount', 'count',
			'total', 'itemCount', 'rowCount', 'dataCount'
		]

		for (const prop of possibleCountProperties) {
			if (multiView[prop] && typeof multiView[prop] === 'number' && multiView[prop] > 0) {
				return multiView[prop]
			}
		}

		// Try items array length
		if (multiView.items && Array.isArray(multiView.items)) {
			return multiView.items.length
		}

		// Try data attributes
		const totalFromAttr = element.getAttribute('data-total-records') ||
							 element.getAttribute('data-record-count') ||
							 element.getAttribute('data-total-count')
		if (totalFromAttr) {
			const parsed = parseInt(totalFromAttr, 10)
			if (!isNaN(parsed) && parsed > 0) {
				return parsed
			}
		}

		return 100 // default fallback
	}

	private _extractRecordCountFromText(text: string): number {

		const ofMatch = text.match(/of\s+(\d+)/i)
		if (ofMatch) {
			const count = parseInt(ofMatch[1], 10)
			return count
		}

		const entriesMatch = text.match(/(\d+)\s+entries/i)
		if (entriesMatch) {
			const count = parseInt(entriesMatch[1], 10)
			return count
		}

		// Pattern 3: Just extract the largest number found
		const allNumbers = text.match(/\d+/g)
		if (allNumbers && allNumbers.length > 0) {
			const count = Math.max(...allNumbers.map(n => parseInt(n, 10)))
			return count
		}

		return 100 // default fallback
	}

	private hasSelectedItems(): boolean {
		// Check if selectedItems are provided as props first
		if (this.selectedItems && this.selectedItems.length > 0) {
			return true
		}

		let hasSelected = false

		// Strategy 1: Search in parent hierarchy
		let parent = this.parentElement
		while (parent && !hasSelected) {
			if (parent.tagName.toLowerCase() === 'lvl-multi-data-view') {
				hasSelected = this._checkSelectedItemsInElement(parent)
				break
			}
			parent = parent.parentElement
		}

		// Strategy 2: Search in the entire document if not found in parents
		if (!hasSelected) {
			const multiViews = document.querySelectorAll('lvl-multi-data-view')

			for (const multiView of multiViews) {
				hasSelected = this._checkSelectedItemsInElement(multiView)
				if (hasSelected) break
			}
		}

		// Strategy 3: Emit event to request selected items from parent (synchronous callback)
		const selectedItemsEvent = new CustomEvent('request-selected-items', {
			detail: {
				callback: (items: any[]) => {
					if (items && Array.isArray(items) && items.length > 0) {
						hasSelected = true
					}
				}
			},
			bubbles: true,
			composed: true
		})
		this.dispatchEvent(selectedItemsEvent)

		return hasSelected
	}

	private _checkSelectedItemsInElement(element: Element): boolean {
		const multiView = element as any

		if (multiView.items && Array.isArray(multiView.items)) {
			const selectedItems = multiView.items.filter((row: any) => {
				// Check multiple possible properties for selection
				return row.selected || row.isSelected || row.checked || row.isChecked
			})
			return selectedItems.length > 0
		}

		return false
	}


	private showOverlay(text: string): void {
		// Emit event for parent to handle overlay instead of accessing global
		this.dispatchEvent(new CustomEvent('show-overlay', {
			detail: { text },
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			if ((window as any).Overlay && (window as any).Overlay.showWait) {
				(window as any).Overlay.showWait(text)
			}
		}, 0)
	}

	private hideOverlay(): void {
		// Emit event for parent to handle overlay instead of accessing global
		this.dispatchEvent(new CustomEvent('hide-overlay', {
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			if ((window as any).Overlay && (window as any).Overlay.hideWait) {
				(window as any).Overlay.hideWait()
			}
		}, 0)
	}

	private showErrorToast(message: string): void {
		// Emit event for parent to handle toast instead of accessing global
		this.dispatchEvent(new CustomEvent('show-toast', {
			detail: {
				heading: this.localize('exportError'),
				text: message,
				type: 'error',
				duration: 5000
			},
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			const toaster = document.getElementById('toaster') as any
			if (toaster && toaster.notify) {
				toaster.notify({
					heading: this.localize('exportError'),
					text: message,
					type: 'error',
					duration: 5000
				})
			}
		}, 0)
	}

	private showSuccessToast(message: string): void {
		// Emit event for parent to handle toast instead of accessing global
		this.dispatchEvent(new CustomEvent('show-toast', {
			detail: {
				heading: this.localize('exportComplete'),
				text: message,
				type: 'success',
				duration: 5000
			},
			bubbles: true,
			composed: true
		}))

		// Fallback to global if no parent handles the event (for backward compatibility)
		setTimeout(() => {
			const toaster = document.getElementById('toaster') as any
			if (toaster && toaster.notify) {
				toaster.notify({
					heading: this.localize('exportComplete'),
					text: message,
					type: 'success',
					duration: 5000
				})
			}
		}, 0)
	}

	private showWarningDialog(recordCount: number, hasPresets: boolean = false): void {
		this._warningRecordCount = recordCount
		this._warningHasPresets = hasPresets
		this._showWarningDialog = true
	}

	private _handleWarningCancel(): void {
		this._showWarningDialog = false
	}

	private async _handleWarningProceed(): Promise<void> {
		this._showWarningDialog = false

		// Follow the exact logic from original _ExcelExport.cshtml
		if (this._warningHasPresets) {
			this.showPresetDialog()
		} else {
			await this.showColumnSelectionDialog()
		}
	}

	private showPresetDialog(): void {
		this._showPresetDialog = true

		// Add event listener for option selection using component-scoped approach
		const optionSelectedHandler = (event: Event) => {
			const customEvent = event as CustomEvent
			if (this._selectableCards && customEvent.detail) {
				this._selectableCards.setAttribute('selected-card', customEvent.detail.value)
			}
		}

		// Use component-scoped event listener instead of document
		this._addComponentEventListener(this, 'option-selected', optionSelectedHandler)

		// Store the handler so we can remove it later
		this._optionSelectedHandler = optionSelectedHandler
	}

	private _handlePresetCancel(): void {
		// Reset the selectable cards state (matches original implementation)
		if (this._selectableCards) {
			this._selectableCards.setAttribute('selected-card', '')
		}
		this._showPresetDialog = false
		this._cleanupOptionSelectedHandler()
	}

	private async _handlePresetNext(): Promise<void> {
		const selectedPresetOption = this._selectableCards?.getAttribute('selected-card') || (this._selectableCards as any)?.selectedCard

		if (!selectedPresetOption || selectedPresetOption.trim() === '') {
			this.showErrorToast(this.localize('selectPresetError'))
			return
		}

		this._showPresetDialog = false
		this._cleanupOptionSelectedHandler()

		if (selectedPresetOption === 'custom') {
			await this.showColumnSelectionDialog()
		} else {
			this.handleExportWithPreset(selectedPresetOption)
		}
	}

	private _cleanupOptionSelectedHandler(): void {
		if (this._optionSelectedHandler) {
			this.removeEventListener('option-selected', this._optionSelectedHandler)
			this._optionSelectedHandler = undefined
		}
	}

	private _cleanupComponentEventListeners(): void {
		// Remove all tracked component event listeners
		this._componentEventListeners.forEach(({ element, event, handler }) => {
			element.removeEventListener(event, handler)
		})
		this._componentEventListeners = []

		// Remove all tracked click handlers
		this._componentClickHandlers.forEach(handler => {
			this.removeEventListener('mousedown', handler)
		})
		this._componentClickHandlers = []
	}

	private _addComponentEventListener(element: Element | Document, event: string, handler: EventListener): void {
		element.addEventListener(event, handler)
		this._componentEventListeners.push({ element, event, handler })
	}

	private async handleExportWithPreset(presetId: string): Promise<void> {
		try {
			const presetColumns = await this.getColumnsForPreset(presetId)
			await this.executeExport(presetColumns)
		} catch (error) {
			this.showErrorToast(this.localize('exportErrorGeneric'))
		}
	}

	private async getColumnsForPreset(presetId: string): Promise<ColumnData[]> {
		try {
			const apiUrl = `/Api/ExcelPresets/${presetId}/PerDataSource/Columns`

			// Use direct fetch like the working old implementation
			const response = await fetch(apiUrl)

			if (!response.ok) {
				throw new Error('Failed to load preset columns')
			}

			const columnsData = await response.json()

			if (!columnsData || !Array.isArray(columnsData)) {
				throw new Error('No columns data received from server')
			}

			// Map the response exactly like the old working implementation
			return columnsData.map((item: any) => ({
				key: item.columnName,
				display: item.columnName,
				fieldId: item.dataFieldId,
				id: item.id,
				dataType: item.dataType,
				columnOrder: item.columnOrder
			}))
		} catch (error) {
			throw error
		}
	}

	private async showColumnSelectionDialog(): Promise<void> {
		// If no columns are available, try to initialize them
		if (this.visibleColumns.length === 0 && this.additionalColumns.length === 0) {
			await this.initializeColumns()
			// If still no columns after initialization, something is wrong
			if (this.visibleColumns.length === 0 && this.additionalColumns.length === 0) {
				return
			}
		}

		this._showColumnSelectionDialog = true
	}

	private _handleColumnSelectionCancel(): void {
		this._showColumnSelectionDialog = false
		// If presets are available, show the preset dialog
		if (this.exportPresets && this.exportPresets.length > 0) {
			this.showPresetDialog()
		}
	}

	private async _handleColumnSelectionExport(): Promise<void> {
		const selectedVisibleColumns = this.getSelectedColumns('visible-columns-list')
		const selectedAdditionalColumns = this.getSelectedColumns('additional-info-list')
		const allSelectedColumns = [...selectedVisibleColumns, ...selectedAdditionalColumns]

		if (allSelectedColumns.length === 0) {
			this.showErrorToast(this.localize('selectColumnsError'))
			return
		}

		this._showColumnSelectionDialog = false
		await this.executeExport(allSelectedColumns)
	}

	private _handleSelectAll(event: MouseEvent): void {
		const button = event.target as HTMLElement
		const targetListId = button.getAttribute('data-target') || ''
		this.handleSelectAll(targetListId, true)
	}

	private _handleDeselectAll(event: MouseEvent): void {
		const button = event.target as HTMLElement
		const targetListId = button.getAttribute('data-target') || ''
		this.handleSelectAll(targetListId, false)
	}

	private _handleSearchToggle(event: MouseEvent): void {
		const button = event.target as HTMLElement
		const target = button.getAttribute('data-target') || ''

		// Find the search bar based on the target
		let searchBar: HTMLElement | null = null
		if (target === 'visible-columns-search') {
			searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="visible-columns-search-bar"]') as HTMLElement
		} else if (target === 'info-columns-search') {
			searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="info-columns-search-bar"]') as HTMLElement
		}

		if (searchBar) {
			// Toggle search bar visibility
			const isVisible = searchBar.style.display !== 'none'
			searchBar.style.display = isVisible ? 'none' : 'block'

			if (!isVisible) {
				// Show search bar and focus on input
				setTimeout(() => {
					const searchInput = searchBar.shadowRoot?.querySelector('input') as HTMLInputElement
					if (searchInput) {
						searchInput.focus()
						// Add search functionality
						this._setupSearchInput(searchInput, target)
					}
				}, 100)
			} else {
				// Hide search bar and clear search
				this._clearSearch(target)
			}
		}
	}

	private _setupSearchInput(searchInput: HTMLInputElement, target: string): void {
		// Remove any existing event listeners
		const newInput = searchInput.cloneNode(true) as HTMLInputElement
		searchInput.parentNode?.replaceChild(newInput, searchInput)

		// Add new event listener for real-time search
		newInput.addEventListener('input', (event) => {
			const searchTerm = (event.target as HTMLInputElement).value.toLowerCase()
			this._filterList(target, searchTerm)
		})

		// Add escape key to close search
		newInput.addEventListener('keydown', (event) => {
			if (event.key === 'Escape') {
				this._clearSearch(target)
			}
		})

		// Add Enter key to close search (like old implementation)
		newInput.addEventListener('keydown', (event) => {
			if (event.key === 'Enter') {
				this._clearSearch(target)
			}
		})

		// Set up click-outside-to-close functionality (like old implementation)
		this._setupClickOutsideToClose(target)
	}

	private _setupClickOutsideToClose(target: string): void {
		// Remove any existing click handler for this target
		this._removeClickOutsideHandler(target)

		const handleComponentClick = (event: Event) => {
			const clickTarget = event.target as Node

			// Get the dialog element to check if click is within the dialog
			const dialog = this.shadowRoot?.querySelector('lvl-dialog[name="column-selection-dialog"]')
			if (!dialog) return

			// Check if click is within the dialog at all
			const isClickInDialog = dialog.contains(clickTarget) ||
								   (dialog.shadowRoot && dialog.shadowRoot.contains(clickTarget))

			if (!isClickInDialog) return

			// Get the search bar and related elements
			let searchBar: HTMLElement | null = null
			let searchButton: HTMLElement | null = null

			if (target === 'visible-columns-search') {
				searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="visible-columns-search-bar"]') as HTMLElement
				searchButton = this.shadowRoot?.querySelector('lvl-button[data-target="visible-columns-search"]') as HTMLElement
			} else if (target === 'info-columns-search') {
				searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="info-columns-search-bar"]') as HTMLElement
				searchButton = this.shadowRoot?.querySelector('lvl-button[data-target="info-columns-search"]') as HTMLElement
			}

			// Check if search is currently active
			if (!searchBar || searchBar.style.display !== 'block') {
				return
			}

			// Simple check: if click is on search bar or search button, don't close
			const isClickOnSearchBar = searchBar.contains(clickTarget) || searchBar === clickTarget
			const isClickOnSearchButton = searchButton && (searchButton.contains(clickTarget) || searchButton === clickTarget)

			// Also check shadow DOM of search bar
			let isClickInSearchBarShadow = false
			if (searchBar.shadowRoot) {
				isClickInSearchBarShadow = searchBar.shadowRoot.contains(clickTarget)
			}

			const isClickInside = isClickOnSearchBar || isClickOnSearchButton || isClickInSearchBarShadow

			if (!isClickInside) {
				this._clearSearch(target)
			}
		}

		// Add to component with a longer delay to prevent immediate triggering
		setTimeout(() => {
			this._addComponentEventListener(this, 'mousedown', handleComponentClick)
			// Store the handler for cleanup
			this._componentClickHandlers.push(handleComponentClick)
		}, 300)
	}

	private _removeClickOutsideHandler(_target: string): void {
		// Remove existing handlers to prevent duplicates
		// Note: In the old implementation, this removed ALL click handlers
		// which is correct because each search setup removes all previous handlers
		this._componentClickHandlers.forEach(handler => {
			this.removeEventListener('mousedown', handler)
		})
		this._componentClickHandlers = []
	}

	private _filterList(target: string, searchTerm: string): void {
		const listId = target === 'visible-columns-search' ? 'visible-columns-list' : 'additional-info-list'
		const list = this.shadowRoot?.getElementById(listId)

		if (list) {
			const listItems = list.shadowRoot?.querySelectorAll('lvl-list-line')

			listItems?.forEach((line) => {
				const textContent = line.textContent?.toLowerCase() || ''
				const shouldShow = searchTerm === '' || textContent.includes(searchTerm)

				const lineElement = line as HTMLElement
				lineElement.style.display = shouldShow ? '' : 'none'
			})
		}
	}

	private _clearSearch(target: string): void {
		// Hide search bar
		let searchBar: HTMLElement | null = null
		if (target === 'visible-columns-search') {
			searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="visible-columns-search-bar"]') as HTMLElement
		} else if (target === 'info-columns-search') {
			searchBar = this.shadowRoot?.querySelector('lvl-search[data-target="info-columns-search-bar"]') as HTMLElement
		}

		if (searchBar) {
			searchBar.style.display = 'none'

			// Clear search input
			const searchInput = searchBar.shadowRoot?.querySelector('input') as HTMLInputElement
			if (searchInput) {
				searchInput.value = ''
			}

			// Show all list items
			this._filterList(target, '')

			// Clean up global click handlers
			this._removeClickOutsideHandler(target)
		}
	}



	private handleSelectAll(targetListId: string, select: boolean): void {
		const list = this.shadowRoot?.getElementById(targetListId)

		if (list) {
			const checkboxes = list.shadowRoot?.querySelectorAll('lvl-list-line-item lvl-checkbox')

			checkboxes?.forEach((checkbox) => {
				const isChecked = (checkbox as any).checked
				if ((select && !isChecked) || (!select && isChecked)) {
					(checkbox as HTMLElement).click()
				}
			})

			// Toggle visibility of buttons using classes
			const section = list.closest('lvl-section')
			const selectBtn = section?.querySelector('[data-action="select-all"]') as HTMLElement
			const deselectBtn = section?.querySelector('[data-action="deselect-all"]') as HTMLElement

			if (selectBtn && deselectBtn) {
				if (select) {
					selectBtn.classList.add('excel-exporter-hidden')
					deselectBtn.classList.remove('excel-exporter-hidden')
				} else {
					selectBtn.classList.remove('excel-exporter-hidden')
					deselectBtn.classList.add('excel-exporter-hidden')
				}
			}
		}
	}



	private getSelectedColumns(listId: string): ColumnData[] {
		const list = this.shadowRoot?.getElementById(listId)
		if (!list) return []

		const checkboxes = list.shadowRoot?.querySelectorAll('lvl-list-line-item lvl-checkbox')
		const selectedColumns: ColumnData[] = []

		checkboxes?.forEach((checkbox, index) => {
			if ((checkbox as any).checked) {
				const columnData = listId === 'visible-columns-list'
					? this.visibleColumns[index]
					: this.additionalColumns[index]
				selectedColumns.push(columnData)
			}
		})

		return selectedColumns
	}

	private async executeExport(selectedColumns: ColumnData[]): Promise<void> {
		const processId = uuid()

		try {
			// Notify parent component
			this.onExportStart?.()

			// Emit event for parent to handle progress tracking instead of accessing global
			this.dispatchEvent(new CustomEvent('start-progress', {
				detail: {
					processId,
					title: this.localize('exportData'),
					description: this.localize('exportWaitMessage'),
					icon: 'file-export'
				},
				bubbles: true,
				composed: true
			}))

			// Fallback to global ProgressSocket if no parent handles the event
			let progressSocket: any = null
			setTimeout(() => {
				const ProgressSocket = (window as any).ProgressSocket
				if (ProgressSocket) {
					progressSocket = new ProgressSocket()
					progressSocket.startProcess(processId, {
						title: this.localize('exportData'),
						description: this.localize('exportWaitMessage'),
						icon: 'file-export'
					})
				}
			}, 0)

			// Get selected items using component-scoped approach
			let selectedItems = this.selectedItems || []

			// Emit event to request selected items from parent if not provided
			if (selectedItems.length === 0) {
				const selectedItemsEvent = new CustomEvent('request-selected-items', {
					detail: { callback: (items: any[]) => { selectedItems = items || [] } },
					bubbles: true,
					composed: true
				})
				this.dispatchEvent(selectedItemsEvent)

				// Fallback: try to find in closest multi-view element
				if (selectedItems.length === 0) {
					let parent = this.parentElement
					while (parent && selectedItems.length === 0) {
						if (parent.tagName.toLowerCase() === 'lvl-multi-data-view') {
							const multiView = parent as any
							selectedItems = multiView?.items?.filter((row: any) => row.selected) || []
							break
						}
						parent = parent.parentElement
					}
				}
			}

			const selectedIds = selectedItems.map((item: any) => item.id)

			// Set up abort controller for timeout
			const controller = new AbortController()
			const timeoutId = setTimeout(() => controller.abort(), 300000) // 5 minutes timeout

			// Make the export API call
			const response = await fetch('/Api/Stream/Export/Data', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Process-ID': processId
				},
				body: JSON.stringify({
					columns: selectedColumns,
					selectedIds: selectedIds
				}),
				signal: controller.signal
			})

			clearTimeout(timeoutId)

			if (!response.ok) {
				let errorMessage = this.localize('exportErrorGeneric')
				try {
					const errorData = await response.json()
					errorMessage = errorData.message || errorData.error || errorMessage
				} catch (e) {
					errorMessage = response.statusText || errorMessage
				}

				// Emit event for parent to handle progress update
				this.dispatchEvent(new CustomEvent('update-progress', {
					detail: {
						processId,
						status: errorMessage,
						progress: 0
					},
					bubbles: true,
					composed: true
				}))

				// Fallback to global progressSocket
				if (progressSocket) {
					progressSocket.updateModalStatus(errorMessage)
					progressSocket.updateModalProgress(0)
				}

				this.showErrorToast(errorMessage)
				this.onExportComplete?.(false)
				return
			}

			// Handle file download using modern approach
			const blob = await response.blob()
			const contentDisposition = response.headers.get('Content-Disposition')
			const filename = contentDisposition
				? contentDisposition.split('filename=')[1].split("''").pop()?.replace(/"/g, '') || 'export.xlsx'
				: 'export.xlsx'

			// Use modern file download approach without direct DOM manipulation
			this._downloadFile(blob, filename)

			// Show success message
			this.showSuccessToast(this.localize('exportCompletedSuccessfully'))
			this.onExportComplete?.(true)

		} catch (error) {
			let errorMessage = this.localize('exportErrorGeneric')

			if (error instanceof Error) {
				if (error.name === 'AbortError') {
					errorMessage = this.localize('exportErrorTimeout')
				} else if (error.message) {
					errorMessage = error.message
				}
			}

			// Emit event for parent to handle progress update
			this.dispatchEvent(new CustomEvent('update-progress', {
				detail: {
					processId,
					status: errorMessage,
					progress: 0
				},
				bubbles: true,
				composed: true
			}))

			// Fallback to global progressSocket
			const ProgressSocket = (window as any).ProgressSocket
			if (ProgressSocket) {
				const progressSocket = new ProgressSocket()
				progressSocket.updateModalStatus(errorMessage)
				progressSocket.updateModalProgress(0)
			}

			this.showErrorToast(errorMessage)
			this.onExportComplete?.(false)
		} finally {
			// Emit event for parent to handle progress cleanup
			this.dispatchEvent(new CustomEvent('cleanup-progress', {
				detail: { processId },
				bubbles: true,
				composed: true
			}))

			// Fallback cleanup for global progressSocket
			setTimeout(() => {
				const ProgressSocket = (window as any).ProgressSocket
				if (ProgressSocket) {
					const progressSocket = new ProgressSocket()
					progressSocket.destroy()
				}
			}, 2000)
		}
	}

	private _downloadFile(blob: Blob, filename: string): void {
		// Create a temporary URL for the blob
		const url = URL.createObjectURL(blob)

		// Create a temporary anchor element within the component's shadow DOM
		const downloadAnchor = document.createElement('a')
		downloadAnchor.href = url
		downloadAnchor.download = filename
		downloadAnchor.style.display = 'none'

		// Append to shadow root if available, otherwise to component
		const container = this.shadowRoot || this
		container.appendChild(downloadAnchor)

		// Trigger download
		downloadAnchor.click()

		// Clean up
		container.removeChild(downloadAnchor)
		URL.revokeObjectURL(url)
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class
declare global {
	interface HTMLElementTagNameMap {
		'lvl-excel-exporter': ExcelExporter
	}
}
