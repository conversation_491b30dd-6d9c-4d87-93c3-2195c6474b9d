import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { ExcelImporterType } from './types'
import { ifDefined } from 'lit/directives/if-defined.js'
import { http } from 'msw'
import { DataType } from '@/enums/data-type.ts'
import { Align } from '@/enums/align.ts'

// Import all required components
import('./ExcelImporter')
import('../selectable-cards/SelectableCards')
import('../dialog/Dialog')
import('../atomics/button/Button')
import('../data-organizers/section/Section')
import('../list-views/data-list/List')
import('../list-views/data-list/ListColumn')
import('../list-views/data-list/ListDataColumn')
import('../inputs/searchbar/Searchbar')
import('../list-views/multi-data-view/table/Table')
import('../list-views/multi-data-view/table/TableDataColumn')
import('../list-views/multi-data-view/table/TableRow')

type ExcelImporterPropertiesExternal = Partial<ExcelImporterType>
type ExcelImporterStory = Story<ExcelImporterPropertiesExternal>

const mockVisibleColumns = [
	{ key: 'name', display: 'Name', fieldId: 'field1', dataType: 'string' },
	{ key: 'email', display: 'Email', fieldId: 'field2', dataType: 'string' },
	{ key: 'age', display: 'Age', fieldId: 'field3', dataType: 'number' }
]

const mockAdditionalColumns = [
	{ key: 'active', display: 'Active Status', fieldId: 'field4', dataType: 'boolean' },
	{ key: 'created', display: 'Created Date', fieldId: 'field5', dataType: 'datetime' },
	{ key: 'department', display: 'Department', fieldId: 'field6', dataType: 'string' },
	{ key: 'salary', display: 'Salary', fieldId: 'field7', dataType: 'number' },
	{ key: 'lastLogin', display: 'Last Login', fieldId: 'field8', dataType: 'datetime' },
	{ key: 'phone', display: 'Phone Number', fieldId: 'field9', dataType: 'string' },
	{ key: 'address', display: 'Address', fieldId: 'field10', dataType: 'string' }
]

const mockImportPresets = [
	{ label: 'Favorite', value: 'favorite-preset', status: true },
	{ label: 'All', value: 'all-preset', status: true },
	{ label: 'Custom', value: 'custom', status: true }
]

// Mock API response format that matches the actual server response
const mockApiPresets = [
	{ id: 'favorite-preset', name: 'Favorite', isActive: true },
	{ id: 'all-preset', name: 'All', isActive: true }
]

// Sample data that would be imported - more realistic dataset
const sampleImportData = [
	{ name: 'John Doe', email: '<EMAIL>', age: 30, active: true, created: '2024-01-15' },
	{ name: 'Jane Smith', email: '<EMAIL>', age: 25, active: false, created: '2024-01-10' },
	{ name: 'Bob Johnson', email: '<EMAIL>', age: 35, active: true, created: '2024-01-20' },
	{ name: 'Alice Brown', email: '<EMAIL>', age: 28, active: true, created: '2024-01-12' },
	{ name: 'Charlie Wilson', email: '<EMAIL>', age: 42, active: false, created: '2024-01-08' },
	{ name: 'Diana Prince', email: '<EMAIL>', age: 29, active: true, created: '2024-01-18' },
	{ name: 'Edward Norton', email: '<EMAIL>', age: 38, active: true, created: '2024-01-22' },
	{ name: 'Fiona Green', email: '<EMAIL>', age: 31, active: false, created: '2024-01-05' },
	{ name: 'George Miller', email: '<EMAIL>', age: 45, active: true, created: '2024-01-25' },
	{ name: 'Helen Davis', email: '<EMAIL>', age: 27, active: true, created: '2024-01-14' }
]

const allMockColumns = [...mockVisibleColumns, ...mockAdditionalColumns]

// Setup function to initialize mock environment (similar to export panel)
const setupMockEnvironment = () => {
	// Setup mock environment
	if (!(window as any).I18n) {
		(window as any).I18n = {
			translate: (key: string, fallback?: string) => {
				const translations: Record<string, string> = {
					'importPresetHeading': 'Import Preset',
					'pickImportPreset': 'Please select an import preset:',
					'cancel': 'Cancel',
					'next': 'Next',
					'importColumnsHeading': 'Import Columns',
					'selectColumnsInfo': 'Select the columns you want to import:',
					'visibleFields': 'Visible Fields',
					'additionalInformation': 'Additional Information',
					'selectAll': 'Select All',
					'deselectAll': 'Deselect All',
					'columns': 'Columns',
					'import': 'Import',
					'selectPresetError': 'Please select a preset',
					'selectColumnsError': 'Please select at least one column',
					'importData': 'Import Data',
					'importWaitMessage': 'Please wait while your data is being imported...',
					'importCompletedSuccessfully': 'Import completed successfully',
					'importErrorGeneric': 'An error occurred during import',
					'importError': 'Import Error',
					'importComplete': 'Import Complete'
				}
				return translations[key] || fallback || key
			}
		}
	}

	if (!document.querySelector('lvl-data-source')) {
		const mockDataSource = document.createElement('lvl-data-source')
		mockDataSource.setAttribute('data-id', 'test-datasource-123')
		mockDataSource.style.display = 'none'
		document.body.appendChild(mockDataSource)
	}

	// Mock XLSX library for Excel file processing
	if (!(window as any).XLSX) {
		(window as any).XLSX = {
			read: () => ({
				SheetNames: [mockExcelData.sheetName],
				Sheets: {
					[mockExcelData.sheetName]: {}
				}
			}),
			utils: {
				sheet_to_json: () => mockExcelData.data
			},
			write: () => new ArrayBuffer(8)
		}
	}

	// Mock fetch API to prevent 404 errors (fallback if MSW doesn't work)
	if (!(window as any).originalFetch) {
		(window as any).originalFetch = window.fetch
		window.fetch = async (url: RequestInfo | URL, options?: RequestInit) => {
			const urlString = typeof url === 'string' ? url : url instanceof URL ? url.href : (url as Request).url

			// Mock Excel presets API calls
			if (urlString.includes('/Api/ExcelPresets/') && urlString.includes('/PerDataSource')) {
				if (urlString.includes('/Columns')) {
					// Mock preset columns response
					return new Response(JSON.stringify([
						{ columnName: 'Name', dataFieldId: 'field1', id: '1', dataType: 'string', columnOrder: 1 },
						{ columnName: 'Email', dataFieldId: 'field2', id: '2', dataType: 'string', columnOrder: 2 }
					]), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				} else {
					// Mock presets list response
					return new Response(JSON.stringify(mockApiPresets), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}
			}

			// Mock import API calls
			if (urlString.includes('/Api/Import/') || urlString.includes('/Api/PageViews/ExcelImport')) {
				return new Response(JSON.stringify({
					success: true,
					message: 'Import completed successfully',
					summary: { inserted: 8, updated: 2, skipped: 0, failed: 0 },
					recordsProcessed: 10
				}), {
					status: 200,
					headers: { 'Content-Type': 'application/json' }
				})
			}

			// Fall back to original fetch for other requests
			return (window as any).originalFetch(url, options)
		}
	}
}

// Mock Excel data that would be loaded from a file
const mockExcelData = {
	data: [
		// Header row (when isFirstLineHeader = true)
		['Full Name', 'Email Address', 'Age', 'Status', 'Join Date'],
		// Data rows
		['John Doe', '<EMAIL>', '30', 'Active', '2024-01-15'],
		['Jane Smith', '<EMAIL>', '25', 'Inactive', '2024-01-10'],
		['Bob Johnson', '<EMAIL>', '35', 'Active', '2024-01-20'],
		['Alice Brown', '<EMAIL>', '28', 'Active', '2024-01-12'],
		['Charlie Wilson', '<EMAIL>', '42', 'Inactive', '2024-01-08'],
		['Diana Prince', '<EMAIL>', '29', 'Active', '2024-01-18'],
		['Edward Norton', '<EMAIL>', '38', 'Active', '2024-01-22'],
		['Fiona Green', '<EMAIL>', '31', 'Inactive', '2024-01-05'],
		['George Miller', '<EMAIL>', '45', 'Active', '2024-01-25'],
		['Helen Davis', '<EMAIL>', '27', 'Active', '2024-01-14']
	],
	fileName: 'sample-import.xlsx',
	sheetName: 'Sheet1'
}

const meta: Meta = {
	title: 'Components/Import Panel/Excel Importer',
	component: 'lvl-excel-importer',
	render: (args: ExcelImporterPropertiesExternal) => html`
		<style>
			:root {
				--clr-background-lvl-0: #ffffff;
				--clr-background-lvl-1: #f8f9fa;
				--clr-background-lvl-2: #e9ecef;
				--clr-text-primary: #212529;
				--clr-text-primary-negativ: #ffffff;
				--clr-text-secondary-positiv: #6c757d;
				--clr-state-active-hover: #0d6efd;
				--clr-border: #dee2e6;
				--size-radius-m: 0.375rem;
			}
			.import-toolbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px;
				background: #f8f9fa;
				border: 1px solid #ddd;
				border-bottom: none;
			}
			.data-table-container {
				border: 1px solid #ddd;
				border-top: none;
				height: 500px;
			}
			/* Override table text styling to make it more visible */
			.data-table-container lvl-table {
				--clr-text-primary: #212529 !important;
				--clr-text-secondary: #495057 !important;
				color: #212529 !important;
				font-size: 14px !important;
			}
			/* Ensure all table content is dark and readable */
			.data-table-container lvl-table * {
				color: #212529 !important;
			}
			.data-table-container lvl-table th,
			.data-table-container lvl-table td {
				color: #212529 !important;
				font-weight: 500 !important;
			}

			/* Ensure dialog text is dark and readable */
			lvl-dialog {
				--cp-clr-text-primary-positiv: #212529 !important;
				--cp-clr-text-secondary: #495057 !important;
				color: #212529 !important;
			}

			/* Force all dialog content to have dark text */
			lvl-dialog * {
				color: #212529 !important;
			}

			/* Specific styling for import dialog elements */
			.import-info-text,
			.import-toggle-label,
			.import-action-label {
				color: #212529 !important;
				font-weight: 500 !important;
			}
		</style>
		<div style="padding: 20px; background: white; min-height: 1000px; width: 100%; max-width: 1200px;">
			<h3>Excel Importer Component</h3>
			<p>This component uses proper Lit rendering with state-based dialog management. Click "Import Data" to test the import functionality.</p>

			<!-- Import Toolbar -->
			<div class="import-toolbar">
				<span>Sample data to be imported (${sampleImportData.length} records)</span>
				<lvl-button
					type="primary"
					icon="file-import"
					label="Import Data"
					size="small"
					@click=${() => {
						setupMockEnvironment()
						const importer = document.querySelector('lvl-excel-importer') as any
						if (importer) {
							// Pre-populate the component with mock Excel data for better demo
							importer._excelData = {
								data: mockExcelData.data,
								sheetNames: [mockExcelData.sheetName],
								currentSheet: mockExcelData.sheetName
							}
							importer._originalFileName = mockExcelData.fileName
							importer._importConfiguration = {
								isFirstLineHeader: true,
								isDuplicateCheckEnabled: false,
								actionForDuplicates: 'PerElement',
								selectedMappings: []
							}

							// Set the import presets directly on the component
							importer.importPresets = mockImportPresets

							// Use the proper component method to trigger import flow
							setTimeout(() => {
								try {
									// Call the component's public method to show dialogs properly
									if (importer.importPresets && importer.importPresets.length > 0) {
										importer['showPresetDialog']()
									} else {
										importer['showPreviewDialog']()
									}
								} catch (error) {
									console.error('Error starting import flow:', error)
								}
							}, 100)
						} else {
							console.error('Importer not found')
						}
					}}>
				</lvl-button>
			</div>

			<!-- Data Table using LIT Table Component -->
			<div class="data-table-container">
				<lvl-table
					rows="${JSON.stringify(sampleImportData)}"
					style="width: 100%; height: 100%; font-size: 14px; --clr-text-primary: #212529; color: #212529;">
					<lvl-table-data-column name="name" label="Name" style="font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="email" label="Email Address"></lvl-table-data-column>
					<lvl-table-data-column name="age" label="Age" type="${DataType.Integer}" text-align="${Align.Right}"></lvl-table-data-column>
					<lvl-table-data-column name="active" label="Status" type="${DataType.Boolean}" text-align="${Align.Center}"></lvl-table-data-column>
					<lvl-table-data-column name="created" label="Created Date" type="${DataType.Date}"></lvl-table-data-column>
				</lvl-table>
			</div>

			<lvl-excel-importer
				data-source-id=${ifDefined(args.dataSourceId)}
				view-id=${ifDefined(args.viewId)}
				page-data-source-id=${ifDefined(args.pageDataSourceId)}
				.pageViewColumns=${args.pageViewColumns || []}
				.importPresets=${args.importPresets || []}
				.onImportStart=${args.onImportStart}
				.onImportComplete=${args.onImportComplete}
				.onImportProgress=${args.onImportProgress}
			>
			</lvl-excel-importer>
		</div>
	`,
	parameters: {
		docs: {
			description: {
				component: `
Excel Importer component for importing data from Excel files with preset support and column mapping.

## Key Features:
- **Proper Lit Rendering**: Uses Lit's reactive state management
- **State-based Dialogs**: All dialogs are rendered conditionally using component state
- **Memory Leak Prevention**: Proper cleanup of event listeners and resources
- **Multiple Import Flows**: Supports preset selection, preview dialog, and column mapping
- **Responsive Design**: Adapts to different screen sizes and data volumes

## Component Architecture:
The ExcelImporter is a functional component that provides import capabilities without visual elements.
It renders all dialogs directly in the render() method using conditional rendering:
- Preset Dialog: Shows available import presets
- Preview Dialog: Shows data preview and header configuration
- Column Mapping Dialog: Allows users to map Excel columns to database fields
- Error Dialog: Shows import errors and validation issues
- Duplicate Dialog: Handles duplicate record resolution

## Usage:
Call the \`startImport()\` method to begin the import process. The component will automatically
show the appropriate dialogs based on available presets and data configuration.
				`
			}
		},
		msw: {
			handlers: [
				// Mock API for fetching columns
				http.get('/Api/PageViews/:viewId/Columns', () => {
					return new Response(JSON.stringify({
						visibleColumns: mockVisibleColumns,
						invisibleColumns: mockAdditionalColumns,
						dataSourceId: 'test-datasource-123'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for fetching presets
				http.get('/Api/ExcelPresets/:dataSourceId/PerDataSource', () => {
					return new Response(JSON.stringify(mockApiPresets), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for getting preset columns
				http.get('/Api/ExcelPresets/:presetId/PerDataSource/Columns', ({ params }) => {
					const presetId = params.presetId as string
					let columns: any[] = []

					if (presetId === 'favorite-preset') {
						// Favorite preset - only 2 columns (most commonly used)
						columns = [
							{ columnName: 'Name', dataFieldId: 'field1', id: '1', dataType: 'string', columnOrder: 1 },
							{ columnName: 'Email', dataFieldId: 'field2', id: '2', dataType: 'string', columnOrder: 2 }
						]
					} else if (presetId === 'all-preset') {
						// All preset - all available columns
						columns = [
							{ columnName: 'Name', dataFieldId: 'field1', id: '1', dataType: 'string', columnOrder: 1 },
							{ columnName: 'Email', dataFieldId: 'field2', id: '2', dataType: 'string', columnOrder: 2 },
							{ columnName: 'Age', dataFieldId: 'field3', id: '3', dataType: 'number', columnOrder: 3 },
							{ columnName: 'Active Status', dataFieldId: 'field4', id: '4', dataType: 'boolean', columnOrder: 4 },
							{ columnName: 'Created Date', dataFieldId: 'field5', id: '5', dataType: 'datetime', columnOrder: 5 },
							{ columnName: 'Department', dataFieldId: 'field6', id: '6', dataType: 'string', columnOrder: 6 },
							{ columnName: 'Salary', dataFieldId: 'field7', id: '7', dataType: 'number', columnOrder: 7 },
							{ columnName: 'Last Login', dataFieldId: 'field8', id: '8', dataType: 'datetime', columnOrder: 8 },
							{ columnName: 'Phone Number', dataFieldId: 'field9', id: '9', dataType: 'string', columnOrder: 9 },
							{ columnName: 'Address', dataFieldId: 'field10', id: '10', dataType: 'string', columnOrder: 10 }
						]
					}

					return new Response(JSON.stringify(columns), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for Excel import
				http.post('/Api/PageViews/ExcelImport', () => {
					return new Response(JSON.stringify({
						success: true,
						summary: { inserted: 8, updated: 2, skipped: 0, failed: 0 },
						errors: [],
						duplicates: []
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for file upload
				http.post('/Api/Stream/Import/Excel', () => {
					// Simulate file upload response
					return new Response(JSON.stringify({
						success: true,
						fileId: 'mock-file-123',
						fileName: 'sample-import.xlsx',
						rowCount: sampleImportData.length
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				})
			]
		}
	},
	argTypes: {
		dataSourceId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'ID of the data source for import'
		},
		viewId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'ID of the view for column configuration'
		},
		pageDataSourceId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'Alternative data source ID from page context'
		}
	}
}

export default meta

//#region stories

export const Default: ExcelImporterStory = {
	args: {
		dataSourceId: 'test-datasource-123',
		viewId: 'test-view-456',
		pageViewColumns: allMockColumns,
		importPresets: mockImportPresets,
		onImportStart: () => {},
		onImportComplete: (_success: boolean, _errors?: any[]) => {},
		onImportProgress: (_progress: number, _status: string) => {}
	},
	parameters: {
		docs: {
			description: {
				story: 'Default configuration showing the complete import flow with presets and column mapping. Click "Import Data" to see the preset dialog followed by preview and column mapping dialogs.'
			}
		}
	}
}

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default)
} as const
