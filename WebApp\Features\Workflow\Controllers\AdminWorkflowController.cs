using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Features.Workflow.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DataType = Levelbuild.Core.SharedDtos.Enums.DataType;

namespace Levelbuild.Frontend.WebApp.Features.Workflow.Controllers;

/// <summary>
/// Controller for Workflow configuration
/// </summary>
public class AdminWorkflowController : AdminController<WorkflowDto>
{
	/// <inheritdoc />
	public AdminWorkflowController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory stringLocalizerFactory, IVersionReader versionReader) : base(logManager, logManager.GetLoggerForClass<AdminWorkflowController>(), contextFactory, userManager, stringLocalizerFactory, versionReader)
	{
	}

	#region Views

	/// <summary>
	/// Renders the detail view with help of the workflow dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="dataSourceSlug">readable identifier for a specific data source</param>
	/// <param name="slug">readable identifier for a specific workflow</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Workflows/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/{dataSourceSlug?}/Workflows/{slug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/{moduleSlug?}/DataSources/{dataSourceSlug?}/Workflows/{slug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? dataSourceSlug, string? slug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new WorkflowForm(ViewType.Edit));
		}
		
		var dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug);
		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug '{dataStoreSlug}' could not be found");
		
		var dataSource = DatabaseContext.DataSources
			.FirstOrDefault(dataSource => dataSource.DataStoreId == dataStore.Id && 
										  dataSource.Slug == dataSourceSlug);
		if (dataSource == null)
			throw new ElementNotFoundException($"DataSource configuration with slug '{dataSourceSlug}' could not be found");
		
		var workflow = DatabaseContext.Workflows
			.Include(workflow => workflow.DataSource)
				.ThenInclude(source => source.Fields)
			.FirstOrDefault(workflow => workflow.DataSourceId == dataSource.Id && 
										workflow.Slug == slug);
		if (workflow == null)
			throw new ElementNotFoundException($"Views with slug '{slug}' could not be found in data source {dataSource.Name}");

		return RenderPartial(new WorkflowForm(ViewType.Edit)
		{
			Workflow = workflow.ToDto()
		});
	}

	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Workflows/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/DataSources/{dataSourceSlug?}/Workflows/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/{moduleSlug?}/DataSources/{dataSourceSlug?}/Workflows/Create")]
	public IActionResult Create(string? dataStoreSlug, string? dataSourceSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(dataSourceSlug))
		{
			return CachedPartial() ?? RenderPartial(new WorkflowForm() { Workflow = new WorkflowDto() });
		}

		var dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug);
		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug '{dataStoreSlug}' could not be found");
		
		var dataSource = DatabaseContext.DataSources
			.FirstOrDefault(dataSource => dataSource.DataStoreId == dataStore.Id && 
										  dataSource.Slug == dataSourceSlug);
		if (dataSource == null)
			throw new ElementNotFoundException($"DataSource configuration with slug '{dataSourceSlug}' could not be found");

		Request.RouteValues.Add("menu", "Workflows");
		return CachedPartial() ?? RenderPartial(
		new WorkflowForm()
		{
		   Workflow = new WorkflowDto() { DataSourceId = dataSource.Id }
		}, "~/Features/DataSource/Views/Detail.cshtml", new DataSourceForm(ViewType.Edit)
		{
			DataSource = dataSource.ToDto()
		});
	}

	#endregion
	
	#region Endpoints
	
	/// <inheritdoc />
	[HttpGet("/Api/Workflows")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var query = DatabaseContext.Workflows
			.Include(workflow => workflow.Nodes);
		return HandleQueryRequest<WorkflowEntity, WorkflowDto>(query, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/Workflows/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		var query = DatabaseContext.Workflows
			.Include(workflow => workflow.DataSource)
				.ThenInclude(source => source.DataStore)
			.Include(workflow => workflow.StatusField)
			.Include(workflow => workflow.RecipientsField)
			.Include(workflow => workflow.Nodes);
		return HandleGetRequest<WorkflowEntity, WorkflowDto>(query, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/Workflows")]
	public override async Task<ActionResult<FrontendResponse>> Create(WorkflowDto dto)
	{
		if(dto.Name == null)
			return GetBadRequestResponse($"Views name cannot be empty.");
		
		var existingWorkflow = await DatabaseContext.Workflows.FirstOrDefaultAsync(workflow => workflow.DataSourceId == dto.DataSourceId && EF.Functions.Like(workflow.Name, dto.Name));
		if (existingWorkflow != null)
			return GetBadRequestResponse($"Workflow with name '{dto.Name}' already exists in this data source!");
		
		DataSourceEntity? dataSource = null;
		if (dto.StatusFieldId == null)
		{
			dataSource = await DatabaseContext.DataSources
				.Include(source => source.DataStore)
				.FirstOrDefaultAsync(source => source.Id == dto.DataSourceId);

			if (dataSource == null)
				return GetBadRequestResponse($"DataSource with id '{dto.DataSourceId}' could not be found.");
			
			var statusField = await DatabaseContext.DataFields.FirstOrDefaultAsync(field => field.DataSourceId == dto.DataSourceId && field.Name == dto.StatusFieldName);
			if (statusField == null)
			{
				statusField = new DataFieldEntity()
				{
					DataSourceId = dto.DataSourceId,
					DataSource = dataSource,
					Name = dto.StatusFieldName,
					FieldType = DataFieldType.DataField,
					Type = DataType.String,
					Mandatory = true,
					AutoGenerated = true
				};
				await DatabaseContext.DataFields.AddAsync(statusField);
				statusField.SetContext(DatabaseContext);
				statusField.CreateField();
				await DatabaseContext.SaveChangesAsync();
			}
			dto.StatusFieldId = statusField.Id;
		}
		
		if (dto.RecipientsFieldId == null)
		{
			dataSource ??= await DatabaseContext.DataSources
				.Include(source => source.DataStore)
				.FirstOrDefaultAsync(source => source.Id == dto.DataSourceId);

			if (dataSource == null)
				return GetBadRequestResponse($"DataSource with id '{dto.DataSourceId}' could not be found.");
			
			var recipientField = await DatabaseContext.DataFields.FirstOrDefaultAsync(field => field.DataSourceId == dto.DataSourceId && 
																					field.Name == dto.RecipientsFieldName);
			if (recipientField == null)
			{
				recipientField = new DataFieldEntity()
				{
					DataSourceId = dto.DataSourceId,
					DataSource = dataSource,
					Name = dto.RecipientsFieldName,
					FieldType = DataFieldType.DataField,
					Type = DataType.String,
					Multi = true,
					Mandatory = true,
					AutoGenerated = true,
					Length = 255
				};
				await DatabaseContext.DataFields.AddAsync(recipientField);
				recipientField.SetContext(DatabaseContext);
				recipientField.CreateField();
				await DatabaseContext.SaveChangesAsync();
			}
			dto.RecipientsFieldId = recipientField.Id;
		}
		
		return await HandleCreateRequestAsync(DatabaseContext.Workflows, dto, CreateAction);
		
		void CreateAction(WorkflowEntity entity)
		{
			var startNode = new WorkflowNodeEntity()
			{
				Workflow = entity,
				Name = "Start",
				Icon = "rocket-launch",
				State = WorkflowNodeState.Start,
				Sorting = 0
			};
			entity.Nodes.Add(startNode);
			
			dataSource!.Touch();
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/Workflows/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, WorkflowDto dto)
	{
		var originalInstance = await DatabaseContext.Workflows.FindAsync(id);
		if (originalInstance == null)
			return GetNotFoundResponse($"Workflow#{id} does not exist");
		
		var originalName = originalInstance.Name;
		if (dto.Name != null && originalName != dto.Name)
		{
			var existingWorkflow = DatabaseContext.Workflows.FirstOrDefault(workflow => workflow.Id != id && 
																						workflow.DataSourceId == dto.DataSourceId && 
																						EF.Functions.Like(workflow.Name, dto.Name));
			if (existingWorkflow != null)
				return GetBadRequestResponse($"Workflow with name '{dto.Name}' already exists in this data source!");
		}
		
		return await HandleUpdateRequestAsync(DatabaseContext.Workflows, id, dto, UpdateAction);
		
		void UpdateAction(WorkflowEntity entity)
		{
			DatabaseContext.DataSources.Find(entity.DataSourceId)?.Touch();

			// Rename status/recipients fields if workflow name changes
			if (dto.Name == null || originalName == dto.Name) 
				return;
			
			var statusField = DatabaseContext.DataFields.Find(entity.StatusFieldId)!;
			statusField.RenameField(statusField.Name, dto.StatusFieldName);
			statusField.Name = dto.StatusFieldName;
				
			var recipientsField = DatabaseContext.DataFields.Find(entity.RecipientsFieldId)!;
			recipientsField.RenameField(recipientsField.Name, dto.RecipientsFieldName);
			recipientsField.Name = dto.RecipientsFieldName;
		}
	}

	/// <inheritdoc />
	[HttpDelete("/Api/Workflows/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Workflows, id, DeleteAction);
		
		void DeleteAction(WorkflowEntity entity)
		{
			DatabaseContext.DataSources.Find(entity.DataSourceId)?.Touch();
		}
	}
	
	#endregion
}