using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.GalleryView;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.Page.Controllers;
using Levelbuild.Frontend.WebApp.Features.PageView.ViewModels;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Features.PageView.Controllers;

/// <summary>
/// Controller for the configuration view of page views
/// </summary>
public class PageViewController : AdminController<PageViewDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public PageViewController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<PageViewController>(), contextFactory, userManager, localizerFactory, versionReader)
	{

	}
	
	#region Views
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/PageViews/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/BasicSettings/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/BasicSettings/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/BasicSettings/Create")]
	public IActionResult Create(string? dataStoreSlug, string? pageSlug, [FromQuery(Name = "type")] PageType pageType)
	{
		Request.RouteValues.Add("menu", "BasicSettings"); // we cant use {menu=BasicSettings} inside the url because this would interfere with PageHeaderElement/Create
		
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(pageSlug))
			return CachedPartial() ?? RenderPartial(new PageViewForm() {PageType = pageType});
		
		PageEntity? pageEntity = DatabaseContext.Pages.FirstOrDefault(page => page.Slug == pageSlug.ToLower());
		if (pageEntity == null)
			throw new ElementNotFoundException($"Page configuration with slug: {pageSlug} could not be found");
		
		return RenderPartial(new PageViewForm()
		{
			PageId = pageEntity.Id,
			PageType = pageEntity.Type
		}, "~/Features/Page/Views/Detail.cshtml", AdminPageController.GetPageFormByType(DatabaseContext, pageEntity.Type, pageEntity.Id));
	}

	/// <summary>
	/// Renders the detail view with help of the data store and page dto
	/// </summary>
	/// <param name="viewSlug">readable identifier for a view</param>
	/// <param name="pageViewType">which type of PageView should be displayed?</param>
	/// <param name="pageViewId">unique identifier of page view</param>
	/// <param name="appPage">is the page this view is linked to a app page?</param>
	/// <param name="dataStoreSlug">readable identifier for a dataStore</param>
	/// <param name="pageSlug">readable identifier for a page</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/PageViews/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Pages/{pageSlug}/Views/{viewSlug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/Views/{viewSlug}/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug}/Modules/{moduleSlug}/DataSources/{dataSourceSlug}/Pages/{pageSlug}/Views/{viewSlug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? pageSlug, string? viewSlug, [FromQuery(Name = "type")] PageViewType? pageViewType,
								[FromQuery(Name = "id")] Guid? pageViewId, [FromQuery(Name = "appPage")] bool appPage = false)
	{
		if (viewSlug == "Create")
			return RedirectToAction("Create", new RouteValueDictionary(new { dataStoreSlug, pageSlug, menu = "Views" }));
		
		if (viewSlug.IsNullOrEmpty() && !pageViewId.HasValue)
		{
			if (pageViewType == null)
				throw new Exception($"viewSlug is required in order to render empty PageViews/Details.cshtml");
			
			return CachedPartial(pageViewType.ToString()) ?? RenderPartial(GetPageViewFormByType(pageViewType.Value, appPage));
		}
		
		// load base view to determine view type
		PageViewEntity? entity;
		if (pageViewId.HasValue)
		{
			entity = DatabaseContext.PageViews.FirstOrDefault(pageView => pageView.Id == pageViewId.Value);
		}
		else
		{
			if(dataStoreSlug.IsNullOrEmpty())
				throw new ElementNotFoundException($"DataStoreSlug can not be null or empty");
			if(pageSlug.IsNullOrEmpty())
				throw new ElementNotFoundException($"PageSlug can not be null or empty");
			var dataStore = DatabaseContext.DataStoreConfigs.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug!.ToLower());
			if (dataStore == null)
				throw new ElementNotFoundException($"DataStore with slug: {dataStoreSlug} could not be found");
			
			var pageEntity = DatabaseContext.Pages.FirstOrDefault(page => page.DataStoreId == dataStore.Id && page.Slug == pageSlug!.ToLower());
			if (pageEntity == null)
				throw new ElementNotFoundException($"Page with slug: {pageSlug} could not be found");
			
			entity = DatabaseContext.PageViews.FirstOrDefault(pageView => pageView.PageId == pageEntity.Id && pageView.Slug == viewSlug);
		}
		
		if (entity == null)
			throw new ElementNotFoundException($"PageView with slug: {viewSlug} could not be found under page with slug: {pageSlug}");
		
		return RenderPartial(GetPageViewFormByType(entity.Type, appPage, entity.Id));
	}
	
	private PageViewForm GetPageViewFormByType(PageViewType type, bool appPage, Guid? id = null)
	{
		return GetPageViewFormByType(DatabaseContext, type, appPage, id);
	}
	
	/// <summary>
	/// Prepare a different PageViewForm depending on the PageViewType of the element
	/// </summary>
	/// <param name="dbContext">db context for selection the PageViewEntity</param>
	/// <param name="type">type of the PageViewEntity</param>
	/// <param name="appPage">are we rendering an app page?</param>
	/// <param name="id">id of the element to load</param>
	/// <returns>Specific PageViewForm (either ListViewForm, GalleryViewForm or GridViewForm)</returns>
	/// <exception cref="ElementNotFoundException"></exception>
	/// <exception cref="Exception"></exception>
	public static PageViewForm GetPageViewFormByType(CoreDatabaseContext dbContext, PageViewType type, bool appPage, Guid? id = null)
	{
		switch (type)
		{
			case PageViewType.List:
				if (id == null)
					return new ListViewForm(ViewType.Edit) { PageViewType = type, AppPage = appPage};
				
				ListViewEntity? listViewEntity = dbContext.ListViews
					.Include(listView => listView.Page)
					.Include(listView => listView.Page.DataSource)
					.Include(listView => listView.Page.DataSource.DataStore)
					.Include(listView => listView.Page.DataSource.Workflows)
					.AsSplitQuery()
					.FirstOrDefault(listView => listView.Id == id);
				
				if (listViewEntity == null)
					throw new ElementNotFoundException($"ListView with id: {id} could not be found");
				
				return new ListViewForm(ViewType.Edit, listViewEntity.ToDto())
				{
					PageViewType = type,
					PageId = listViewEntity.PageId,
					AppPage = listViewEntity.Page.AppPage
				};
			case PageViewType.Gallery:
				if (id == null)
					return new GalleryViewForm(ViewType.Edit) { PageViewType = type, AppPage = appPage };
				
				GalleryViewEntity? galleryViewEntity = dbContext.GalleryViews
					.Include(listView => listView.Page)
					.Include(listView => listView.Page.DataSource)
					.Include(listView => listView.Page.DataSource.DataStore)
					.Include(listView => listView.TitleField)
					.Include(listView => listView.SubtitleField)
					.AsNoTracking()
					.AsSplitQuery()
					.FirstOrDefault(listView => listView.Id == id);
				
				if (galleryViewEntity == null)
					throw new ElementNotFoundException($"GalleryView with id: {id} could not be found");
				
				return new GalleryViewForm(ViewType.Edit, galleryViewEntity.ToDto())
				{
					PageViewType = type,
					PageId = galleryViewEntity.PageId,
					AppPage = galleryViewEntity.Page.AppPage
				};
			case PageViewType.Grid:
				if (id == null)
					return new GridViewForm(ViewType.Edit) { PageViewType = type, AppPage = appPage };

				GridViewEntity? gridViewEntity = dbContext.GridViews
					.Include(gridView => gridView.Page)
					.ThenInclude(page => page.DataSource).ThenInclude(dataSource => dataSource.DataStore)
					.Include(gridView => gridView.Sections)
					.Include(gridView => gridView.Pages).ThenInclude(page => page.EmbeddedPage)
					.AsSplitQuery()
					.AsNoTracking()
					.FirstOrDefault(gridView => gridView.Id == id);
				
				if (gridViewEntity == null)
					throw new ElementNotFoundException($"GridView with id: {id} could not be found");

				var dto = new GridViewForm(ViewType.Edit, gridViewEntity.ToDto())
				{
					PageViewType = type,
					PageId = gridViewEntity.PageId,
					AppPage = gridViewEntity.Page.AppPage
				};
				
				return dto;
			default:
				throw new Exception($"Editing PageViews of type  \"{type.ToString()}\" is not yet supported!");
		}
	}
	
	#endregion
	
	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/PageViews/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		return HandleQueryRequest<PageViewEntity, PageViewDto>(DatabaseContext.PageViews, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/PageViews/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		// switch by type
		var pageView = DatabaseContext.PageViews.FirstOrDefault(pageView => pageView.Id == id);
		if (pageView == null)
			return GetNotFoundResponse($"Page-view with id: {id} could not be found");

		switch (pageView.Type)
		{
			case PageViewType.List:
				var listViewQuery = DatabaseContext.ListViews
					.Include(listView => listView.Page)
					.Include(listView => listView.Page.DataSource)
					.Include(listView => listView.Page.DataSource.DataStore);

				return HandleGetRequest<ListViewEntity, ListViewDto>(listViewQuery, id);

			case PageViewType.Gallery:
				var galleryViewQuery = DatabaseContext.GalleryViews
						.Include(listView => listView.Page)
						.Include(listView => listView.Page.DataSource)
						.Include(listView => listView.Page.DataSource.DataStore)
						.Include(listView => listView.TitleField)
						.Include(listView => listView.SubtitleField);
				return HandleGetRequest<GalleryViewEntity, GalleryViewDto>(galleryViewQuery, id);
			
			case PageViewType.Grid:
				var gridViewQuery = DatabaseContext.GridViews
					.Include(listView => listView.Page)
					.Include(listView => listView.Page.DataSource)
					.Include(listView => listView.Page.DataSource.DataStore);
				
				return HandleGetRequest<GridViewEntity, GridViewDto>(gridViewQuery, id);
		}

		return GetUnprocessableEntityResponse($"Type \"{pageView.Type.ToString()}\" is not yet supported!");
	}
	
	/// <inheritdoc />
	[HttpPost("/Api/PageViews/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] PageViewDto pageViewDto)
	{
		switch (pageViewDto.Type)
		{
			case PageViewType.List:
				return await HandleCreateRequestAsync(DatabaseContext.ListViews, (ListViewDto)pageViewDto);
			case PageViewType.Gallery:
				return await HandleCreateRequestAsync(DatabaseContext.GalleryViews, (GalleryViewDto)pageViewDto);
			case PageViewType.Grid:
				return await HandleCreateRequestAsync(DatabaseContext.GridViews, (GridViewDto)pageViewDto);
			default:
				return GetUnprocessableEntityResponse($"Type \"{pageViewDto.Type.ToString()}\" is not yet supported!");
		}
	}

	/// <inheritdoc />
	[HttpPatch("/Api/PageViews/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] PageViewDto configurationDto)
	{
		// check if element exists
		var entity = await DatabaseContext.PageViews.FindAsync(id);
		if (entity == null)
			return GetNotFoundResponse($"PageViewEntity#{id} does not exist");

		// parse JSON into specific DTO according to type
		switch (entity.Type)
		{
			case PageViewType.List:
				return await HandleUpdateRequestAsync(DatabaseContext.ListViews, entity.Id, (ListViewDto)configurationDto);
			case PageViewType.Gallery:
				return await HandleUpdateRequestAsync(DatabaseContext.GalleryViews, entity.Id, (GalleryViewDto)configurationDto);
			case PageViewType.Grid:
				return await HandleUpdateRequestAsync(DatabaseContext.GridViews, entity.Id, (GridViewDto)configurationDto);
			default:
				return GetUnprocessableEntityResponse($"PageViewType \"{entity.Type.ToString()}\" is not yet supported!");
		}
	}
	
	/// <inheritdoc />
	[HttpDelete("/Api/PageViews/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.PageViews, id);
	}
	
	/// <summary>
	/// Returns a mutated list of data fields
	/// </summary>
	/// <param name="id">data field id</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	[HttpGet("/Api/PageViews/{id:guid}/DataFields")]
	public ActionResult<FrontendResponse> DataFields(Guid id, QueryParamsDto parameters)
	{
		var pageView = DatabaseContext.PageViews
			.Include(view => view.Page)
			.ThenInclude(page => page.DataSource)
			.ThenInclude(dataSource => dataSource.DataStore)
			.AsSplitQuery()
			.FirstOrDefault(view => view.Id == id);
		
		if (pageView == null)
			return GetNotFoundResponse($"Page View #{id} does not exist");
		
		// collect currently occupied field id's
		var query = DatabaseContext.DataFields.Where(field => field.DataSourceId == pageView.Page.DataSourceId);
		return HandleAutocompleteRequest(query, parameters,
										 nameof(DataFieldEntity.Id),
										 nameof(DataFieldEntity.Name),
										 new PropertyPathList<DataFieldEntity>()
										 {
											 nameof(DataFieldEntity.Name), 
											 nameof(DataFieldEntity.Type),
											 nameof(DataFieldEntity.Slug),
											 (DataFieldEntity field) => field.DataSource!.Slug,
											 (DataFieldEntity field) => field.DataSource!.DataStore.Slug,
										 });
	}
	
	#endregion
}