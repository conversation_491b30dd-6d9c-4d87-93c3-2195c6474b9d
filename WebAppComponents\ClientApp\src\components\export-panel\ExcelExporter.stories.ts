import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html } from 'lit'
import { LevelStory } from '@story-home/support/commands'
import { ExcelExporterType } from './ExcelExporter'
import { ifDefined } from 'lit/directives/if-defined.js'
import { http } from 'msw'
import { DataType } from '@/enums/data-type.ts'
import { Align } from '@/enums/align.ts'

// Import all required components
import('./ExcelExporter')
import('../selectable-cards/SelectableCards')
import('../dialog/Dialog')
import('../atomics/button/Button')
import('../data-organizers/section/Section')
import('../list-views/data-list/List')
import('../list-views/data-list/ListColumn')
import('../list-views/data-list/ListDataColumn')
import('../inputs/searchbar/Searchbar')
import('../list-views/multi-data-view/table/Table')
import('../list-views/multi-data-view/table/TableDataColumn')
import('../list-views/multi-data-view/table/TableRow')

type ExcelExporterPropertiesExternal = Partial<ExcelExporterType>
type ExcelExporterStory = Story<ExcelExporterPropertiesExternal>

const mockVisibleColumns = [
	{ key: 'name', display: 'Name', fieldId: 'field1', dataType: 'string' },
	{ key: 'email', display: 'Email', fieldId: 'field2', dataType: 'string' },
	{ key: 'age', display: 'Age', fieldId: 'field3', dataType: 'number' }
]

const mockAdditionalColumns = [
	{ key: 'active', display: 'Active Status', fieldId: 'field4', dataType: 'boolean' },
	{ key: 'created', display: 'Created Date', fieldId: 'field5', dataType: 'datetime' },
	{ key: 'department', display: 'Department', fieldId: 'field6', dataType: 'string' },
	{ key: 'salary', display: 'Salary', fieldId: 'field7', dataType: 'number' },
	{ key: 'lastLogin', display: 'Last Login', fieldId: 'field8', dataType: 'datetime' },
	{ key: 'phone', display: 'Phone Number', fieldId: 'field9', dataType: 'string' },
	{ key: 'address', display: 'Address', fieldId: 'field10', dataType: 'string' }
]

const mockExportPresets = [
	{ label: 'Favorite', value: 'favorite-preset', status: true },
	{ label: 'All', value: 'all-preset', status: true },
	{ label: 'Custom', value: 'custom', status: true }
]

// Mock API response format that matches the actual server response
const mockApiPresets = [
	{ id: 'favorite-preset', name: 'Favorite', isActive: true },
	{ id: 'all-preset', name: 'All', isActive: true }
]

// Realistic employee data for demonstration
const tableRows = [
	{ name: 'Sarah Johnson', email: '<EMAIL>', age: 32, active: true, created: '2023-03-15', department: 'Engineering' },
	{ name: 'Michael Chen', email: '<EMAIL>', age: 28, active: true, created: '2023-06-10', department: 'Product Design' },
	{ name: 'Emily Rodriguez', email: '<EMAIL>', age: 35, active: true, created: '2022-11-20', department: 'Marketing' },
	{ name: 'David Thompson', email: '<EMAIL>', age: 41, active: false, created: '2021-08-12', department: 'Sales' },
	{ name: 'Jessica Williams', email: '<EMAIL>', age: 29, active: true, created: '2023-09-08', department: 'Human Resources' },
	{ name: 'Robert Kim', email: '<EMAIL>', age: 37, active: true, created: '2022-05-18', department: 'Engineering' },
	{ name: 'Amanda Foster', email: '<EMAIL>', age: 26, active: true, created: '2024-01-22', department: 'Customer Success' },
	{ name: 'James Wilson', email: '<EMAIL>', age: 44, active: false, created: '2020-12-05', department: 'Finance' },
	{ name: 'Lisa Anderson', email: '<EMAIL>', age: 33, active: true, created: '2023-04-25', department: 'Operations' },
	{ name: 'Christopher Lee', email: '<EMAIL>', age: 39, active: true, created: '2022-07-14', department: 'Engineering' },
	{ name: 'Maria Garcia', email: '<EMAIL>', age: 31, active: true, created: '2023-02-11', department: 'Marketing' },
	{ name: 'Kevin Brown', email: '<EMAIL>', age: 27, active: true, created: '2023-10-16', department: 'Product Design' },
	{ name: 'Rachel Davis', email: '<EMAIL>', age: 36, active: false, created: '2021-09-19', department: 'Sales' },
	{ name: 'Daniel Martinez', email: '<EMAIL>', age: 42, active: true, created: '2022-01-23', department: 'Engineering' },
	{ name: 'Nicole Taylor', email: '<EMAIL>', age: 30, active: true, created: '2023-07-09', department: 'Customer Success' },
	{ name: 'Andrew Miller', email: '<EMAIL>', age: 38, active: true, created: '2022-10-14', department: 'Operations' },
	{ name: 'Stephanie White', email: '<EMAIL>', age: 25, active: true, created: '2024-02-05', department: 'Human Resources' },
	{ name: 'Brandon Clark', email: '<EMAIL>', age: 34, active: false, created: '2021-11-30', department: 'Finance' },
	{ name: 'Jennifer Lewis', email: '<EMAIL>', age: 40, active: true, created: '2022-03-17', department: 'Marketing' },
	{ name: 'Ryan Walker', email: '<EMAIL>', age: 29, active: true, created: '2023-08-21', department: 'Engineering' }
]

const mockSelectedItems = tableRows

// Helper function to setup mock environment for all stories
const setupMockEnvironment = () => {
	if (!(window as any).I18n) {
		(window as any).I18n = {
			translate: (key: string, fallback?: string, ...replacements: any[]) => {
				const translations: Record<string, string> = {
					'exportPresetHeading': 'Export Preset',
					'pickExportPreset': 'Please select an export preset:',
					'cancel': 'Cancel',
					'next': 'Next',
					'exportColumnsHeading': 'Export Columns',
					'selectColumnsInfo': 'Select the columns you want to export:',
					'visibleFields': 'Visible Fields',
					'additionalInformation': 'Additional Information',
					'selectAll': 'Select All',
					'deselectAll': 'Deselect All',
					'columns': 'Columns',
					'export': 'Export',
					'selectPresetError': 'Please select a preset',
					'selectColumnsError': 'Please select at least one column',
					'exportData': 'Export Data',
					'exportWaitMessage': 'Please wait while your data is being exported...',
					'exportCompletedSuccessfully': 'Export completed successfully',
					'exportErrorGeneric': 'An error occurred during export',
					'exportError': 'Export Error',
					'exportComplete': 'Export Complete',
					'unusuallyHighExports': 'Unusually High Number of Exports',
					'largeExportWarning': 'You are about to export {0} records. This may take some time. Do you want to continue?',
					'yes': 'Yes',
					'no': 'No',
					'loadingAvailablePresets': 'Loading available presets...'
				}
				let result = translations[key] || fallback || key
				// Handle placeholder replacement
				if (replacements && replacements.length > 0) {
					replacements.forEach((replacement, index) => {
						result = result.replace(`{${index}}`, replacement)
					})
				}
				return result
			}
		}
	}

	if (!document.querySelector('lvl-data-source')) {
		const mockDataSource = document.createElement('lvl-data-source')
		mockDataSource.setAttribute('data-id', 'test-datasource-123')
		mockDataSource.style.display = 'none'
		document.body.appendChild(mockDataSource)
	}
}

// Common styles for all stories
const commonStyles = html`
	<style>
		:root {
			--clr-background-lvl-0: #ffffff;
			--clr-background-lvl-1: #f8f9fa;
			--clr-background-lvl-2: #e9ecef;
			--clr-text-primary: #212529;
			--clr-text-primary-negativ: #ffffff;
			--clr-text-secondary-positiv: #6c757d;
			--clr-state-active-hover: #0d6efd;
			--clr-border: #dee2e6;
			--size-radius-m: 0.375rem;
		}

		/* Force dark text for all table content */
		lvl-table {
			--clr-text-primary: #212529 !important;
			--clr-text-secondary: #212529 !important;
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Override any inherited text colors */
		lvl-table * {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Specific targeting for table cells and headers */
		lvl-table th,
		lvl-table td,
		lvl-table .table__cell,
		lvl-table .table__header,
		lvl-table .table__row {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Target shadow DOM content if needed */
		lvl-table::part(cell),
		lvl-table::part(header),
		lvl-table::part(row) {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Additional specificity for nested elements */
		div lvl-table,
		div lvl-table *,
		.data-table-container lvl-table,
		.data-table-container lvl-table * {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Specific class for our export demo table */
		.export-demo-table {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		.export-demo-table * {
			color: #212529 !important;
			font-weight: 500 !important;
		}

		/* Force text color in all possible table elements */
		.export-demo-table .table__cell,
		.export-demo-table .table__header-cell,
		.export-demo-table .table__row,
		.export-demo-table td,
		.export-demo-table th,
		.export-demo-table span,
		.export-demo-table div {
			color: #212529 !important;
			font-weight: 500 !important;
		}
	</style>
`

const meta: Meta = {
	title: 'Components/Export Panel/Excel Exporter',
	component: 'lvl-excel-exporter',
	render: (args: ExcelExporterPropertiesExternal) => html`
		${commonStyles}
		<div style="padding: 20px; background: white; min-height: 1000px; width: 100%; max-width: 1200px;">
			<h3>Excel Exporter Component</h3>
			<p>This component uses proper Lit rendering with state-based dialog management. Click "Export Data" to test the export functionality.</p>

			<!-- Data table with integrated export functionality -->
			<div style="margin-top: 20px;">
				<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px;">
					<div>
						<h4 style="margin: 0;">Employee Data Table</h4>
						<span style="font-size: 0.9em; color: #666;">Showing ${(args.selectedItems || tableRows).length} records</span>
					</div>
					<lvl-button
						type="primary"
						icon="file-export"
						label="Export Data"
						size="small"
						@click=${() => {
							setupMockEnvironment()
							const exporter = document.querySelector('lvl-excel-exporter') as any
							if (exporter && exporter.startExport) {
								exporter.startExport()
							}
						}}>
					</lvl-button>
				</div>

				<lvl-table
					class="export-demo-table"
					borderless
					selection-mode="multiple"
					rows='${JSON.stringify(args.selectedItems || tableRows)}'
					style="max-height: 500px; border: 1px solid #e0e0e0; border-radius: 6px; --clr-text-primary: #212529; --clr-text-secondary: #212529; color: #212529; font-weight: 500;">
					<lvl-table-data-column name="name" label="Name" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="email" label="Email" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="age" label="Age" type="${DataType.Integer}" text-align="${Align.Right}" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="department" label="Department" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="active" label="Active" type="${DataType.Boolean}" text-align="${Align.Center}" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
					<lvl-table-data-column name="created" label="Created Date" type="${DataType.Date}" style="color: #212529; font-weight: 500;"></lvl-table-data-column>
				</lvl-table>

				<div style="margin-top: 10px; padding: 8px; background: #f0f0f0; border-radius: 4px; font-size: 0.9em; color: #666;">
					<strong>Available for Export:</strong> ${(args.selectedItems || tableRows).length} records |
					<strong>Visible Columns:</strong> ${(args.visibleColumns || []).length} |
					<strong>Additional Columns:</strong> ${(args.additionalColumns || []).length}
				</div>
			</div>

			<!-- The Excel Exporter Component (hidden, provides functionality) -->
			<lvl-excel-exporter
				data-source-id=${ifDefined(args.dataSourceId)}
				view-id=${ifDefined(args.viewId)}
				.visibleColumns=${args.visibleColumns || []}
				.additionalColumns=${args.additionalColumns || []}
				.exportPresets=${args.exportPresets || []}
				.selectedItems=${args.selectedItems || []}
				.onExportStart=${args.onExportStart}
				.onExportComplete=${args.onExportComplete}
				style="display: block; height: 0; overflow: hidden;">
			</lvl-excel-exporter>
		</div>
	`,
	parameters: {
		docs: {
			description: {
				component: `
Excel Exporter component for exporting data to Excel files with preset support and column selection.

## Key Features:
- **Proper Lit Rendering**: Uses Lit's reactive state management 
- **State-based Dialogs**: All dialogs are rendered conditionally using component state
- **Memory Leak Prevention**: Proper cleanup of event listeners and resources
- **Multiple Export Flows**: Supports warning dialog, preset selection, and column selection
- **Responsive Design**: Adapts to different screen sizes and data volumes

## Component Architecture:
The ExcelExporter is a functional component that provides export capabilities without visual elements.
It renders all dialogs directly in the render() method using conditional rendering:
- Warning Dialog: Shows for large datasets (>1000 records)
- Preset Dialog: Shows available export presets
- Column Selection Dialog: Allows users to select which columns to export

## Integration Pattern:
The component is typically integrated with data tables where:
1. The ExcelExporter component is placed near the table (often hidden)
2. An "Export" button in the table header calls the \`startExport()\` method
3. The component handles the entire export flow through its dialogs
4. The component automatically determines which dialogs to show based on data size and available presets

## Usage:
Call the \`startExport()\` method from any UI element (button, menu item, etc.) to begin the export process.
				`
			}
		},
		msw: {
			handlers: [
				// Mock API for fetching export presets
				http.get('/Api/ExcelPresets/:dataSourceId/PerDataSource', () => {
					return new Response(JSON.stringify(mockApiPresets), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for fetching columns
				http.get('/Api/PageViews/:viewId/Columns', () => {
					return new Response(JSON.stringify({
						visibleColumns: mockVisibleColumns,
						invisibleColumns: mockAdditionalColumns,
						dataSourceId: 'test-datasource-123'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for Excel export
				http.post('/Api/PageViews/ExcelExport', () => {
					return new Response(JSON.stringify({
						success: true,
						downloadUrl: '/downloads/export-123.xlsx',
						fileName: 'export-data.xlsx'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for getting export data
				http.get('/Api/PageViews/:viewId/ExportData', () => {
					return new Response(JSON.stringify({
						data: mockSelectedItems,
						totalCount: mockSelectedItems.length
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for getting preset columns
				http.get('/Api/ExcelPresets/:presetId/PerDataSource/Columns', ({ params }) => {
					const presetId = params.presetId as string
					let columns: any[] = []

					if (presetId === 'favorite-preset') {
						// Favorite preset - only 2 columns (most commonly used)
						columns = [
							{ columnName: 'Name', dataFieldId: 'field1', id: '1', dataType: 'string', columnOrder: 1 },
							{ columnName: 'Email', dataFieldId: 'field2', id: '2', dataType: 'string', columnOrder: 2 }
						]
					} else if (presetId === 'all-preset') {
						// All preset - all available columns
						columns = [
							{ columnName: 'Name', dataFieldId: 'field1', id: '1', dataType: 'string', columnOrder: 1 },
							{ columnName: 'Email', dataFieldId: 'field2', id: '2', dataType: 'string', columnOrder: 2 },
							{ columnName: 'Age', dataFieldId: 'field3', id: '3', dataType: 'number', columnOrder: 3 },
							{ columnName: 'Active Status', dataFieldId: 'field4', id: '4', dataType: 'boolean', columnOrder: 4 },
							{ columnName: 'Created Date', dataFieldId: 'field5', id: '5', dataType: 'datetime', columnOrder: 5 },
							{ columnName: 'Department', dataFieldId: 'field6', id: '6', dataType: 'string', columnOrder: 6 },
							{ columnName: 'Salary', dataFieldId: 'field7', id: '7', dataType: 'number', columnOrder: 7 },
							{ columnName: 'Last Login', dataFieldId: 'field8', id: '8', dataType: 'datetime', columnOrder: 8 },
							{ columnName: 'Phone Number', dataFieldId: 'field9', id: '9', dataType: 'string', columnOrder: 9 },
							{ columnName: 'Address', dataFieldId: 'field10', id: '10', dataType: 'string', columnOrder: 10 }
						]
					}

					return new Response(JSON.stringify(columns), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					})
				}),
				// Mock API for export execution
				http.post('/Api/Stream/Export/Data', () => {
					// Simulate file download
					const blob = new Blob(['Mock Excel Data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
					return new Response(blob, {
						status: 200,
						headers: {
							'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
							'Content-Disposition': 'attachment; filename="export-data.xlsx"'
						}
					})
				})
			]
		}
	},
	argTypes: {
		dataSourceId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'ID of the data source for export'
		},
		viewId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'ID of the view for export configuration'
		},
		pageDataSourceId: {
			control: 'text',
			table: {
				type: { summary: 'string' },
				defaultValue: { summary: 'undefined' }
			},
			description: 'Alternative data source ID from page context'
		}
	}
}

export default meta

//#region stories

export const Default: ExcelExporterStory = {
	args: {
		dataSourceId: 'test-datasource-123',
		viewId: 'test-view-456',
		visibleColumns: mockVisibleColumns,
		additionalColumns: mockAdditionalColumns,
		exportPresets: mockExportPresets,
		selectedItems: mockSelectedItems
	},
	parameters: {
		docs: {
			description: {
				story: 'Default configuration showing the complete export flow with presets and column selection. Click "Export Data" to see the preset dialog followed by column selection.'
			}
		}
	}
}



export const stories = {
	default: new LevelStory(meta, Default),
} as const
