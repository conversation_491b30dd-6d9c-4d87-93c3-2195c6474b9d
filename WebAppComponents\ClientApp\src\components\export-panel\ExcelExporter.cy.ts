import { stories } from './ExcelExporter.stories'
import { storyTest } from '@test-home/support/advanced-functions'
import { ExcelExporter } from './ExcelExporter'

import('./ExcelExporter')

// Helper function to setup component with safe defaults
function setupExcelExporterComponent(component: any, options: { recordCount?: number, hasPresets?: boolean } = {}) {
	// Mock record count to avoid warning dialog unless specifically testing it
	component.getRecordCount = () => options.recordCount || 20

	// Ensure component is properly initialized
	component.dataSourceId = 'test-datasource-123'
	component.viewId = 'test-view-456'

	// Mock hasSelectedItems to return false (so warning dialog can be triggered)
	component.hasSelectedItems = () => false

	// Set up export presets if needed
	if (options.hasPresets !== false) {
		component.exportPresets = [
			{ label: 'Favorite', value: 'favorite-preset', status: true },
			{ label: 'All', value: 'all-preset', status: true },
			{ label: 'Custom', value: 'custom', status: true }
		]
	}

	return component
}

// Test suite for the Excel Exporter web component
describe('<lvl-excel-exporter />', () => {

	beforeEach(() => {
		
		// Setup proper localization for tests
		cy.window().then((win) => {
			if (!(win as any).I18n) {
				(win as any).I18n = {
					translate: (key: string, fallback?: string, ...replacements: any[]) => {
						const translations: Record<string, string> = {
							'exportPresetHeading': 'Export Preset',
							'pickExportPreset': 'Please select an export preset:',
							'cancel': 'Cancel',
							'next': 'Next',
							'exportColumnsHeading': 'Export Columns',
							'selectColumnsInfo': 'Select the columns you want to export:',
							'visibleFields': 'Visible Fields',
							'additionalInformation': 'Additional Information',
							'selectAll': 'Select All',
							'deselectAll': 'Deselect All',
							'columns': 'Columns',
							'export': 'Export',
							'selectPresetError': 'Please select a preset',
							'selectColumnsError': 'Please select at least one column',
							'exportData': 'Export Data',
							'exportWaitMessage': 'Please wait while your data is being exported...',
							'exportCompletedSuccessfully': 'Export completed successfully',
							'exportErrorGeneric': 'An error occurred during export',
							'exportError': 'Export Error',
							'exportComplete': 'Export Complete',
							'unusuallyHighExports': 'Unusually High Number of Exports',
							'largeExportWarning': 'You are about to export {0} records. This may take some time. Do you want to continue?',
							'yes': 'Yes',
							'no': 'No',
							'loadingAvailablePresets': 'Loading available presets...'
						}
						let result = translations[key] || fallback || key
						// Handle placeholder replacement
						if (replacements && replacements.length > 0) {
							replacements.forEach((replacement, index) => {
								result = result.replace(`{${index}}`, replacement)
							})
						}
						return result
					}
				}
			}
		})
	})

	storyTest('renders the default component', stories.default, () => {
		cy.mountStory(stories.default)

		// Check component exists (but don't check visibility since it's a functional component with height 0)
		cy.get('lvl-excel-exporter').should('exist')

		// Check that the component has the correct attributes
		cy.get('lvl-excel-exporter')
			.should('have.attr', 'data-source-id', 'test-datasource-123')
			.should('have.attr', 'view-id', 'test-view-456')

		// Check that the story UI elements are present
		cy.contains('Excel Exporter Component').should('be.visible')
		cy.contains('Export Data').should('be.visible')

		// Check that the record count information is displayed in the specific context (avoid warning dialog text)
		cy.get('div').contains('20 records').should('be.visible')

		// Check that the data table is rendered
		cy.get('lvl-table').should('exist')
		cy.get('lvl-table-data-column[name="name"]').should('exist')
		cy.get('lvl-table-data-column[name="email"]').should('exist')
	})

	storyTest('has working export button functionality', stories.default, () => {
		cy.mountStory(stories.default)

		// Wait for component to be fully initialized
		cy.get('lvl-excel-exporter').should('exist')

		// Wait for the component to be connected and initialized
		cy.get('lvl-excel-exporter').then(($el) => {
			const component = setupExcelExporterComponent($el[0] as any)

			// Wait for component to be connected
			cy.wrap(component).should('have.property', 'isConnected', true)

			// Wait for startExport method to be available
			cy.wrap(component).should('have.property', 'startExport')
			cy.wrap(component.startExport).should('be.a', 'function')
		})

		// Click the export button from the story (using lvl-button)
		cy.get('lvl-button').contains('Export Data').should('be.visible').click()

		// Verify component properties are set correctly
		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter
			// Component should be properly initialized
			expect(component.dataSourceId).to.equal('test-datasource-123')
			expect(component.viewId).to.equal('test-view-456')
		})
	})

	storyTest('displays correct properties in default story', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter

			// Check that properties are set correctly
			expect(component.dataSourceId).to.equal('test-datasource-123')
			expect(component.viewId).to.equal('test-view-456')
			expect(component.visibleColumns).to.have.length(3) // mockVisibleColumns has 3 items
			expect(component.additionalColumns).to.have.length(7) // mockAdditionalColumns has 7 items
			expect(component.exportPresets).to.have.length(3) // mockExportPresets has 3 items
			expect(component.selectedItems).to.have.length(20) // mockSelectedItems has 20 items (all tableRows)
		})
	})

	storyTest('handles component with different configurations', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter

			// Test modifying exportPresets to empty array
			component.exportPresets = []
			expect(component.exportPresets).to.have.length(0)

			// Test modifying visibleColumns to minimal set
			const originalColumns = component.visibleColumns
			component.visibleColumns = [originalColumns[0]] // Only first column
			expect(component.visibleColumns).to.have.length(1)

			// Test modifying selectedItems to subset
			const originalItems = component.selectedItems
			component.selectedItems = originalItems?.slice(0, 2) // Only first 2 items
			expect(component.selectedItems).to.have.length(2)

			// Restore original values
			component.exportPresets = [
				{ label: 'Favorite', value: 'favorite-preset', status: true },
				{ label: 'All', value: 'all-preset', status: true },
				{ label: 'Custom', value: 'custom', status: true }
			]
			component.visibleColumns = originalColumns
			component.selectedItems = originalItems
		})
	})

	storyTest('emits custom events', stories.default, () => {
		cy.mountStory(stories.default)

		// Set up event listeners
		cy.window().then((win) => {
			const events: string[] = []

			win.addEventListener('show-overlay', () => {
				events.push('show-overlay')
			})

			win.addEventListener('hide-overlay', () => {
				events.push('hide-overlay')
			})

			win.addEventListener('show-toast', () => {
				events.push('show-toast')
			})

			// Store events array on window for later access
			;(win as any).testEvents = events
		})

		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter

			// Trigger some internal methods that emit events
			;(component as any).showOverlay('Test message')
			;(component as any).hideOverlay()
			;(component as any).showErrorToast('Test error')
		})

		// Check that events were emitted
		cy.window().then((win) => {
			const events = (win as any).testEvents
			expect(events).to.include('show-overlay')
			expect(events).to.include('hide-overlay')
			expect(events).to.include('show-toast')
		})
	})

	storyTest('handles callback functions', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter

			// Check that callback properties can be set and called
			const startCallback = cy.stub()
			const completeCallback = cy.stub()

			component.onExportStart = startCallback
			component.onExportComplete = completeCallback

			// Verify they are now functions
			expect(component.onExportStart).to.be.a('function')
			expect(component.onExportComplete).to.be.a('function')

			// Test calling them
			component.onExportStart?.()
			component.onExportComplete?.(true)

			expect(startCallback).to.have.been.called
			expect(completeCallback).to.have.been.calledWith(true)
		})
	})

	storyTest('handles selected items modification', stories.default, () => {
		cy.mountStory(stories.default)

		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter

			// Modify selected items to simulate selection
			const originalItems = component.selectedItems
			component.selectedItems = originalItems?.slice(0, 2) // First 2 items

			// Verify selected items
			expect(component.selectedItems).to.have.length(2)
			expect(component.selectedItems![0]).to.have.property('name', 'Sarah Johnson')
			expect(component.selectedItems![1]).to.have.property('name', 'Michael Chen')

			// Restore original items
			component.selectedItems = originalItems
		})
	})

	it('mounts all stories without errors', () => {
		Object.values(stories).forEach(story => {
			cy.mountStory(story)
			cy.get('lvl-excel-exporter').should('exist')
		})
	})

	it('has proper component registration', () => {
		cy.window().then((win) => {
			expect(win.customElements.get('lvl-excel-exporter')).to.exist
		})
	})

	it('handles component lifecycle correctly', () => {
		cy.mountStory(stories.default)
		
		cy.get('lvl-excel-exporter').then(($el) => {
			const component = $el[0] as ExcelExporter
			
			// Component should be connected
			expect(component.isConnected).to.be.true
			
			// Test disconnection
			component.remove()
			expect(component.isConnected).to.be.false
		})
	})

	describe('Column Management', () => {
		storyTest('handles visible and additional columns', stories.default, () => {
			cy.mountStory(stories.default)
			
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as ExcelExporter
				
				// Check column arrays
				expect(component.visibleColumns).to.be.an('array')
				expect(component.additionalColumns).to.be.an('array')
				
				// Verify column structure
				if (component.visibleColumns.length > 0) {
					const firstColumn = component.visibleColumns[0]
					expect(firstColumn).to.have.property('key')
					expect(firstColumn).to.have.property('display')
				}
			})
		})
	})

	describe('API Integration', () => {
		storyTest('handles API responses correctly', stories.default, () => {
			cy.intercept('GET', '/Api/ExcelPresets/*/PerDataSource', {
				statusCode: 200,
				body: [
					{ id: 'preset1', name: 'Test Export Preset', isActive: true }
				]
			}).as('getExportPresets')
			
			cy.intercept('POST', '/Api/PageViews/ExcelExport', {
				statusCode: 200,
				body: {
					success: true,
					downloadUrl: '/downloads/test-export.xlsx',
					fileName: 'test-export.xlsx'
				}
			}).as('postExcelExport')
			
			cy.intercept('GET', '/Api/PageViews/*/ExportData', {
				statusCode: 200,
				body: {
					data: [
						{ id: '1', name: 'Test User', email: '<EMAIL>' }
					],
					totalCount: 1
				}
			}).as('getExportData')
			
			cy.mountStory(stories.default)
			
			// Component should render without issues
			cy.get('lvl-excel-exporter').should('exist')
		})
		
		storyTest('handles API errors gracefully', stories.default, () => {
			cy.intercept('GET', '/Api/ExcelPresets/*/PerDataSource', {
				statusCode: 500,
				body: { error: 'Server error' }
			}).as('getPresetsError')
			
			cy.intercept('POST', '/Api/PageViews/ExcelExport', {
				statusCode: 400,
				body: { error: 'Bad request' }
			}).as('postExportError')
			
			cy.mountStory(stories.default)
			
			// Component should still render and not crash
			cy.get('lvl-excel-exporter').should('exist')
		})
	})

	describe('Warning Dialog', () => {
		storyTest('warning dialog renders correctly when state is set', stories.default, () => {
			cy.mountStory(stories.default)

			// Wait for component to be ready
			cy.get('lvl-excel-exporter').should('exist')

			// Directly set the warning dialog state to test rendering
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as any
				component._showWarningDialog = true
				component._warningRecordCount = 2000
				component.requestUpdate()
			})

			// Wait for component to update
			cy.wait(100)

			// Check that warning dialog is rendered with open attribute
			cy.get('lvl-dialog.excel-exporter-dialog-warning[name="warning-dialog"]')
				.should('exist')
				.and('have.attr', 'open')

			// Check that warning text exists and has content
			cy.get('.excel-exporter-warning-text').should('exist')

			// Check that the dialog has the expected buttons (scoped to warning dialog)
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="cancel"]').should('exist')
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="proceed"]').should('exist')
		})

		storyTest('shows warning dialog for large datasets via showWarningDialog method', stories.default, () => {
			cy.mountStory(stories.default)

			// Wait for component to be ready
			cy.get('lvl-excel-exporter').should('exist')

			// Directly call showWarningDialog method to test dialog functionality
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as any
				setupExcelExporterComponent(component)

				// Directly call the showWarningDialog method
				component.showWarningDialog(2000, false)

				// Force component update
				component.requestUpdate()
			})

			// Wait for component to update
			cy.wait(100)

			// Check that the warning dialog state is set
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as any
				expect(component._showWarningDialog).to.be.true
				expect(component._warningRecordCount).to.equal(2000)
			})

			// Wait for warning dialog to appear
			cy.get('lvl-dialog.excel-exporter-dialog-warning[name="warning-dialog"]', { timeout: 5000 })
				.should('exist')
				.and('have.attr', 'open')

			// Check that warning text exists
			cy.get('.excel-exporter-warning-text').should('exist')

			// Check that the dialog has the expected buttons (scoped to warning dialog)
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="cancel"]').should('exist')
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="proceed"]').should('exist')

			// Test clicking No to cancel (scoped to warning dialog, force click due to dialog height issue)
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="cancel"]').click({ force: true })

			// Dialog should close
			cy.get('lvl-dialog[name="warning-dialog"]').should('not.have.attr', 'open')
		})

		storyTest('warning dialog button handlers work correctly', stories.default, () => {
			cy.mountStory(stories.default)

			// Wait for component to be ready
			cy.get('lvl-excel-exporter').should('exist')

			// Directly show warning dialog
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as any
				setupExcelExporterComponent(component)

				// Show warning dialog
				component.showWarningDialog(2000, false)
				component.requestUpdate()
			})

			// Wait for component to update
			cy.wait(100)

			// Wait for warning dialog to appear
			cy.get('lvl-dialog.excel-exporter-dialog-warning[name="warning-dialog"]', { timeout: 5000 })
				.should('exist')
				.and('have.attr', 'open')

			// Test the cancel button functionality (scoped to warning dialog, force click due to dialog height issue)
			cy.get('lvl-dialog[name="warning-dialog"] lvl-button[data-action="cancel"]').should('exist').click({ force: true })

			// Dialog should close
			cy.get('lvl-dialog[name="warning-dialog"]').should('not.have.attr', 'open')

			// Verify the component state is updated
			cy.get('lvl-excel-exporter').then(($el) => {
				const component = $el[0] as any
				expect(component._showWarningDialog).to.be.false
			})
		})
	})

})
